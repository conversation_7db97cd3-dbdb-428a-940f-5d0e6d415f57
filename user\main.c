
//#include "ti_msp_dl_config.h"
//#include "headfile.h"

//int main(void)
//{
//    SYSCFG_DL_init();	      //系统资源配置初始化	
//	OLED_Init();						//显示屏初始化
//	nADC_Init();					  //ADC初始化
//	nGPIO_Init();						//蜂鸣器初始化
//	w25qxx_gpio_init();     //板载w25q64初始化
//	ctrl_params_init();			//控制参数初始化
//	trackless_params_init();//硬件配置初始化
//	simulation_pwm_init();  //模拟PWM初始化
//	rc_range_init();				//遥控器行程初始化
//	rangefinder_init();			//测距传感器-uart3
//	rgb_init();							//RGB灯初始化	
//	Encoder_Init();					//编码器资源初始化
//	Button_Init();					//板载按键初始化
//	PPM_Init();							//接收机PPM信号初始化
//	timer_irq_config();
//    trackless_output.unlock_flag=UNLOCK;//长按后解锁
//  while(1)
//  {
//		screen_display();//屏幕显示
//  }
//}





///***************************************
//函数名:	void duty_200hz(void)
//说明: 200hz实时任务函数
//入口:	无
//出口:	无
//备注:	无
//作者:	无名创新
//***************************************/
//void maple_duty_200hz(void)
//{
//	get_wheel_speed();			   //获取轮胎转速
//	sdk_duty_run();				   //SDK总任务控制
//	motor_output(speed_ctrl_mode); //控制器输出
//	read_button_state_all();       //按键状态读取
//    laser_light_work(&beep);       //电源板蜂鸣器驱动
//}

///***************************************
//函数名:	void duty_1000hz(void)
//说明: 1000hz实时任务函数
//入口:	无
//出口:	无
//备注:	无
//作者:	无名创新



#include "headfile.h"      // 包含所有核心的类型定义和 extern 声明
#include "motor_control.h" // 包含电机控制相关的 extern 变量和函数
#include "gray_detection.h"// 包含灰度检测相关的 extern 变量和函数
// 不需要再包含 sdk.h, subtask.h, user.h, developer_mode.h，
// 因为我们只专注巡线，且相关 extern 变量应通过 headfile.h 或其所包含的头文件引入。

// ****** main.c 中不再需要重复的 extern 声明 ******
// 所有核心变量 (如 sdk_work_mode, speed_expect, turn_ctrl_pwm, turn_scale,
// RC_Data, trackless_output, startpoint_check_flag, smartcar_imu 等)
// 和核心函数 (如 speed_control_100hz, gray_turn_control_200hz, steer_control)
// 都应该通过上述 #include 语句间接引入。
// 如果编译时有“未定义”错误，请检查对应的头文件是否缺失或声明不完整。

  
// main 函数
int main()
{  SYSCFG_DL_init();	      //系统资源配置初始化	
	OLED_Init();						//显示屏初始化
	nADC_Init();					  //ADC初始化
	nGPIO_Init();						//蜂鸣器初始化
	w25qxx_gpio_init();     //板载w25q64初始化
	ctrl_params_init();			//控制参数初始化
	trackless_params_init();//硬件配置初始化
	simulation_pwm_init();  //模拟PWM初始化
	rc_range_init();				//遥控器行程初始化
	rangefinder_init();			//测距传感器-uart3
	rgb_init();							//RGB灯初始化	
	Encoder_Init();					//编码器资源初始化
	Button_Init();					//板载按键初始化
	PPM_Init();							//接收机PPM信号初始化
    // ****** 圈数控制和状态管理变量（静态局部变量）******
    // 这些变量只在 main 函数内部可见，但它们的生命周期贯穿整个程序运行。
    static uint8_t target_laps = 3;       // **设定目标圈数（1~5），这里设置为 3 圈**
    static uint8_t current_laps = 0;      // 当前已完成圈数，初始为 0
    static bool prev_startpoint_check_flag = false; // 用于检测 startpoint_check_flag 的上升沿

    // ****** 系统初始化代码 ******
    // 在这里放置所有硬件初始化、外设配置等一次性执行的代码。
    // 例如：
    // System_Init(); // 你的系统初始化函数
    // gpio_input_init(); // 灰度管GPIO初始化，从 gray_detection.h 提供
    // IMU_Init(); // IMU初始化
    // Timer_Init(); // 定时器初始化，用于触发传感器读取和控制循环
    // trackless_params_init(); // 从 sdk.h 提供，用于初始化各种参数
    // ... 其他初始化 ...

    // ****** 小车运动模式和状态的初始化 ******
    // 从你提供的 sdk.c 片段可知，trackless_output.init 是用于一次性初始化的。
    // 这段逻辑与 sdk_duty_run 中的逻辑一致。
    if (trackless_output.init == 0) // 假设 trackless_output.init 初始为0
    {
        trackless_output.yaw_ctrl_mode = ROTATE;
        trackless_output.yaw_outer_control_output = 0;
        trackless_output.init = 1; // 标记已初始化
        // flight_subtask_reset(); // 如果需要，调用子任务复位，它在 subtask.h
        
        // 确保圈数变量在每次程序启动时被初始化
        current_laps = 0;           
        prev_startpoint_check_flag = false; 
    }

    // 设置初始工作模式为基于灰度管的自主寻迹（巡线）模式
    sdk_work_mode = 1; 
    
    // 设置初始速度期望（根据你的巡线需求调整）
    // speed_setup 也在 motor_control.h 中声明
    speed_setup = 50; // 例如，设置为 50，具体值需要根据你的小车和赛道调整

    // ****** 主循环 ******
    while (1)
    {
        // ***** 周期性任务调用提醒 *****
        // 关键：确保你的传感器读取函数 (如 gpio_input_check_channel_12_with_handle())
        // 和速度控制函数 (speed_control_100hz) 在一个高频率的定时器中断服务函数（ISR）中被周期性调用。
        // 例如，一个 1ms 或 5ms 的定时器中断。
        //
        // 伪代码示例 (ISR 内部)：
        // void Timer_ISR(void) {
        //     gpio_input_check_channel_12_with_handle(); // 更新 startpoint_check_flag 和 gray_status[0]
        //     // smartcar_imu_update(); // 更新 IMU 状态，包括 smartcar_imu.state_estimation.distance
        //     speed_control_100hz(speed_ctrl_mode); // 执行速度 PID 控制
        //     // ... 其他需要周期性执行的高速率任务 ...
        // }
        //
        // 如果没有定时器中断，你也可以在主循环中频繁调用，但要小心阻塞和时序问题：
        // gpio_input_check_channel_12_with_handle(); // 如果不耗时且循环频率够高
        // speed_control_100hz(speed_ctrl_mode);      // 如果不耗时且循环频率够高

        // ****** 巡线模式 (sdk_work_mode == 1) 的核心逻辑 ******
        // 圈数检测与控制逻辑
        // 检测 startpoint_check_flag 的上升沿（从0变为1）来计数圈数。
        // `startpoint_check_flag` 由 `gpio_input_check_channel_12_with_handle()` 更新。
        if (startpoint_check_flag == 1 && prev_startpoint_check_flag == false)
        {
            current_laps++; // 计数增加一圈
            // 可以在这里添加蜂鸣器或其他提示音效，例如：
            // beep.period = 50; beep.light_on_percent = 0.5f; beep.reset = 1; beep.times = 1;
        }
        prev_startpoint_check_flag = (bool)startpoint_check_flag; // 更新前一时刻的标志状态

        // 检查是否达到目标圈数
        if (target_laps > 0 && current_laps >= target_laps)
        {
            // 达到或超过目标圈数，停止小车
            speed_setup = 0;   // 速度期望设为0
            turn_ctrl_pwm = 0; // 转向控制设为0
            speed_expect[0] = 0;
            speed_expect[1] = 0;
            sdk_work_mode = 0; // 切换到停止模式（或你定义的其他停车模式）

            // 提示任务完成，例如蜂鸣器响多声：
            // beep.period = 200; beep.light_on_percent = 0.5f; beep.reset = 1; beep.times = 3;
            
            // 为了完全停止并退出巡线逻辑，进入一个无限循环
            while(1) 
            {
                // 保持小车停止状态
                speed_expect[0] = 0;
                speed_expect[1] = 0;
                speed_control_100hz(speed_ctrl_mode); // 持续调用速度控制，确保停止
                // 可以在这里添加停止指示灯或蜂鸣器持续鸣叫
            }
        }

        // 正常巡线控制 (仅当未达到目标圈数时才执行)
        speed_ctrl_mode = 1; // 速度控制方式为两轮单独控制
        
        // 调用灰度巡线控制函数，它会计算出转向控制量 turn_ctrl_pwm
        // gray_turn_control_200hz 在 gray_detection.h 中声明，并在 gray_detection.c 中实现
        gray_turn_control_200hz(&turn_ctrl_pwm); 
        
        // 期望速度：左右轮速度根据转向控制量进行差速调整
        // turn_scale 也在 gray_detection.h 中声明
        speed_expect[0] = speed_setup + turn_ctrl_pwm * turn_scale; // 左边轮子速度期望
        speed_expect[1] = speed_setup - turn_ctrl_pwm * turn_scale; // 右边轮子速度期望
        
        // speed_control_100hz 函数通常在定时器中断中调用，但为了代码完整性在此再次提及。
        // 它会根据 speed_expect 更新电机输出。
        // speed_control_100hz 在 motor_control.h 中声明
        // speed_control_100hz(speed_ctrl_mode); // 实际调用应在定时器中断中
    }
    // 程序不应到达这里
    return 0; 
}
//***************************************/
void duty_1000hz(void)
{
    gpio_input_check_channel_5();//检测5路灰度灰度管状态
}

/***************************************
函数名:	void duty_100hz(void)
说明: 100hz实时任务函数
入口:	无
出口:	无
备注:	无
作者:	无名创新
***************************************/
void duty_100hz(void)
{

}


/***************************************
函数名:	void duty_10hz(void)
说明: 10hz实时任务函数
入口:	无
出口:	无
备注:	无
作者:	无名创新
***************************************/
void duty_10hz(void)
{

}
// 某个 .c 文件中，例如 maple_control.c 或 user.c
void maple_duty_200hz(void)
{
    // 200Hz 任务的具体逻辑
    // ...
}