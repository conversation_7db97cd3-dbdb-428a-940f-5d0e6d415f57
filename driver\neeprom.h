#ifndef __NEEPROM_H
#define __NEEPROM_H



#define eeprom_mode 2



#define PARAMETER_TABLE_STARTADDR   0x08060000 
#define FLIGHT_PARAMETER_TABLE_NUM  200
typedef struct
{
	float Parameter_Table[FLIGHT_PARAMETER_TABLE_NUM];
	float parameter_table[FLIGHT_PARAMETER_TABLE_NUM];
	bool health[FLIGHT_PARAMETER_TABLE_NUM];
	uint32_t num;
}FLIGHT_PARAMETER;


typedef enum
{
	SPEED_SETUP=0,
  WORK_MODE,	
	ACCEL_X_OFFSET,
	ACCEL_Y_OFFSET,
	ACCEL_Z_OFFSET,
	GYRO_X_OFFSET,
	GYRO_Y_OFFSET,
	GYRO_Z_OFFSET,
  CTRL_TURN_KP1,
	CTRL_TURN_KI1,	
	CTRL_TURN_KD1,
  CTRL_TURN_SCALE,
  CTRL_GYRO_KP,
	CTRL_GY<PERSON>_<PERSON><PERSON>,	
	CTRL_GY<PERSON>_KD,
	CTRL_GYRO_SCALE,
  CTRL_SPEED_KP,
	CTRL_SPEED_KI,
	CTRL_SPEED_KD,

  CTRL_TURN_KP2,
	CTRL_TURN_KI2,	
	CTRL_TURN_KD2,  
	
	CTRL_BALANCE_ANGLE1_KP,
	CTRL_BALANCE_ANGLE1_KI,
	CTRL_BALANCE_ANGLE1_KD,
	CTRL_BALANCE_ANGLE2_KP,
	CTRL_BALANCE_ANGLE2_KI,
	CTRL_BALANCE_ANGLE2_KD,
  CTRL_BALANCE_GYRO_KP,
	CTRL_BALANCE_GYRO_KI,
	CTRL_BALANCE_GYRO_KD,
	
	BALANCE_ANGLE_EXPECT,
	CTRL_BALANCE_SPEED_KP,
	CTRL_BALANCE_SPEED_KI,
	CTRL_BALANCE_SPEED_KD,
	BALANCE_VEL_CTRL_ENABLE,
	BALANCE_DIR_CTRL_ENABLE,
	BALANCE_CTRL_NUMBER,
	
	LEFT_ENC_DIR_CFG,
	RIGHT_ENC_DIR_CFG,
	LEFT_MOVE_DIR_CFG,
	RIGHT_MOVE_DIR_CFG,
	TIRE_RADIUS_CM_CFG,
	PULSE_NPC_CFG,
	SERVO_MEDIAN_VALUE_1,
	SERVO_MEDIAN_VALUE_2,
	RANGEFINDER_TYPE,
	NO_VOLTAGE_ENABLE,
	NO_VOLTAGE_UPPER,
	NO_VOLTAGE_LOWER,
	
	
	
	RESERVED_SPACE_NUM=100,
	RESERVED_PARAMS_1,
	RESERVED_PARAMS_2,
	RESERVED_PARAMS_3,
	RESERVED_PARAMS_4,
	RESERVED_PARAMS_5,
	RESERVED_PARAMS_6,
	RESERVED_PARAMS_7,
	RESERVED_PARAMS_8,
	RESERVED_PARAMS_9,
	RESERVED_PARAMS_10,
	RESERVED_PARAMS_11,
	RESERVED_PARAMS_12,
	RESERVED_PARAMS_13,
	RESERVED_PARAMS_14,
	RESERVED_PARAMS_15,
	RESERVED_PARAMS_16,
	RESERVED_PARAMS_17,
	RESERVED_PARAMS_18,
	RESERVED_PARAMS_19,
}FLIGHT_PARAMETER_TABLE;


void ReadFlashParameterALL(FLIGHT_PARAMETER *WriteData);
void ReadFlashParameterOne(uint16_t index,float *ReadData);
void ReadFlashParameterThree(uint16_t index,float *ReadData1,float *ReadData2,float *ReadData3);


void WriteFlashParameter(uint16_t index,float WriteData,FLIGHT_PARAMETER *Table);										 								 
void WriteFlashParameter_Two(uint16_t index,float WriteData1,float WriteData2,FLIGHT_PARAMETER *Table);														 
void WriteFlashParameter_Three(uint16_t index,float WriteData1,float WriteData2,float WriteData3,FLIGHT_PARAMETER *Table);


extern FLIGHT_PARAMETER Trackless_Params;
#endif


