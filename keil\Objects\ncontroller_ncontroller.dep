Dependencies for Project 'ncontroller', Target 'ncontroller': (DO NOT MODIFY !)
CompilerVersion: 6230000::V6.23::ARMCLANG
F (..\source\ti\driverlib\dl_adc12.c)(0x6877966C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_adc12.o -MMD)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_aes.c)(0x6877966C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aes.o -MMD)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_aesadv.c)(0x6877966C)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_aesadv.o -MMD)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_common.c)(0x6877966D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_common.o -MMD)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
F (..\source\ti\driverlib\dl_crc.c)(0x6877966D)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crc.o -MMD)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_crcp.c)(0x6877966E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_crcp.o -MMD)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_dac12.c)(0x6877966E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dac12.o -MMD)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_dma.c)(0x6877966E)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_dma.o -MMD)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_flashctl.c)(0x6877966F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_flashctl.o -MMD)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
F (..\source\ti\driverlib\dl_i2c.c)(0x6877966F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_i2c.o -MMD)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_keystorectl.c)(0x68779670)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_keystorectl.o -MMD)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_lcd.c)(0x68779670)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lcd.o -MMD)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_lfss.c)(0x68779670)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_lfss.o -MMD)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_mathacl.c)(0x68779670)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mathacl.o -MMD)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_mcan.c)(0x68779671)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_mcan.o -MMD)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_opa.c)(0x68779671)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_opa.o -MMD)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_rtc_common.c)(0x68779672)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_rtc_common.o -MMD)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_spi.c)(0x68779672)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_spi.o -MMD)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_timer.c)(0x68779673)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_timer.o -MMD)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
F (..\source\ti\driverlib\dl_trng.c)(0x68779673)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_trng.o -MMD)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\dl_uart.c)(0x68779673)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_uart.o -MMD)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
F (..\source\ti\driverlib\dl_vref.c)(0x68779674)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_vref.o -MMD)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\driverlib\m0p\dl_interrupt.c)(0x687796AE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_interrupt.o -MMD)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
F (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.c)(0x68779717)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/dl_sysctl_mspm0g1x0x_g3x0x.o -MMD)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
F (..\source\ti\devices\msp\m0p\startup_system_files\keil\startup_mspm0g350x_uvision.s)(0x687798E5)(--cpu Cortex-M0+ -g --diag_suppress=A1950W

--pd "__UVISION_VERSION SETA 542"

--pd "__MSPM0G3507__ SETA 1"

--list .\listings\startup_mspm0g350x_uvision.lst

--xref -o .\objects\startup_mspm0g350x_uvision.o

--depend .\objects\startup_mspm0g350x_uvision.d)
F (..\user\main.c)(0x688B1796)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/main.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\ncontroller.syscfg)(0x687795B5)()
F (..\ti_msp_dl_config.c)(0x687795B5)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ti_msp_dl_config.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
F (..\ti_msp_dl_config.h)(0x687795B5)()
F (..\user\datatype.h)(0x687795D1)()
F (..\user\headfile.h)(0x687795D1)()
F (..\driver\system.c)(0x687795CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/system.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
F (..\driver\oled.c)(0x687795CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/oled.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\oledfont.h)(0x687795CF)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
F (..\driver\ssd1306.c)(0x687795CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ssd1306.o -MMD)
I (..\driver\binary.h)(0x687795CC)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\glcdfont.c)(0x687795CC)
F (..\driver\ntimer.c)(0x687795CE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ntimer.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\ntimer.h)(0x687795CE)
F (..\driver\npwm.c)(0x687795CD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/npwm.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
F (..\driver\nqei.c)(0x687795CE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nqei.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\system.h)(0x687795CF)
I (..\apply\user.h)(0x687795CB)
I (..\driver\nqei.h)(0x687795CE)
F (..\driver\nppm.c)(0x687795CD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nppm.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\system.h)(0x687795CF)
I (..\driver\us100.h)(0x687795CF)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
F (..\driver\nadc.c)(0x687795CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nadc.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\nadc.h)(0x687795CC)
I (..\apply\user.h)(0x687795CB)
F (..\driver\nuart.c)(0x687795CE)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nuart.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\system.h)(0x687795CF)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\nclink.h)(0x687795CA)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\vision.h)(0x687795CB)
I (..\driver\nuart.h)(0x687795CE)
F (..\driver\ni2c.c)(0x687795CD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ni2c.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\ni2c.h)(0x687795CD)
F (..\driver\icm20608.c)(0x687795CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/icm20608.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\system.h)(0x687795CF)
I (..\apply\wp_math.h)(0x687795CC)
I (..\driver\ni2c.h)(0x687795CD)
I (..\apply\sensor.h)(0x687795CB)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\icm20608.h)(0x687795CC)
F (..\driver\nbutton.c)(0x687795CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nbutton.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\nbutton.h)(0x687795CD)
F (..\driver\ngpio.c)(0x687795CD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ngpio.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\ngpio.h)(0x687795CD)
F (..\driver\neeprom.c)(0x687795CD)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/neeprom.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\driver\neeprom.h)(0x687795CD)
F (..\driver\w25qxx.c)(0x687795CF)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/w25qxx.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\driver\system.h)(0x687795CF)
I (..\user\datatype.h)(0x687795D1)
I (..\driver\w25qxx.h)(0x687795CF)
F (..\driver\us100.c)(0x687795D0)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/us100.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\user.h)(0x687795CB)()
F (..\apply\rgb.c)(0x687795CA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/rgb.o -MMD)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\wp_math.c)(0x687795CC)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/wp_math.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\pid.c)(0x687795CA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/pid.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\ui.c)(0x687795CB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/ui.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\rc.c)(0x687795CA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/rc.o -MMD)
I (..\user\Headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\filter.c)(0x687795C9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/filter.o -MMD)
I (..\user\Headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\sensor.c)(0x687795CB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/sensor.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\Fusion\Fusion.h)(0x68779610)
I (..\apply\Fusion\FusionAhrs.h)(0x68779610)
I (..\apply\Fusion\FusionMath.h)(0x68779610)
I (..\apply\Fusion\FusionAxes.h)(0x68779610)
I (..\apply\Fusion\FusionCalibration.h)(0x68779610)
I (..\apply\Fusion\FusionCompass.h)(0x68779610)
I (..\apply\Fusion\FusionOffset.h)(0x68779610)
F (..\apply\gray_detection.c)(0x687795C9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/gray_detection.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\self_balancing.c)(0x687795CB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/self_balancing.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\motor_control.c)(0x687795C9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/motor_control.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\sdk.c)(0x688695F7)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/sdk.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\vision.c)(0x687795CB)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/vision.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\nclink.c)(0x687795CA)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/nclink.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\attitude_selfstable.c)(0x687795C9)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/attitude_selfstable.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\developer\developer_mode.c)(0x6877960F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/developer_mode.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
I (..\apply\user.h)(0x687795CB)
F (..\apply\developer\subtask.c)(0x6877960F)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/subtask.o -MMD)
I (..\user\headfile.h)(0x687795D1)
I (..\user\datatype.h)(0x687795D1)
I (..\user\main.h)(0x687795D1)
I (..\ti_msp_dl_config.h)(0x687795B5)
I (..\source\ti\devices\msp\msp.h)(0x687796AE)
I (..\source\ti\devices\DeviceFamily.h)(0x6877966C)
I (..\source\ti\devices\msp\m0p\mspm0g350x.h)(0x68779708)
I (..\source\third_party\CMSIS\Core\Include\core_cm0plus.h)(0x68779704)
I (..\source\ti\devices\msp\peripherals\hw_adc12.h)(0x68779709)
I (..\source\ti\devices\msp\peripherals\hw_aes.h)(0x6877970A)
I (..\source\ti\devices\msp\peripherals\hw_comp.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_crc.h)(0x6877970B)
I (..\source\ti\devices\msp\peripherals\hw_dac12.h)(0x6877970C)
I (..\source\ti\devices\msp\peripherals\hw_dma.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_flashctl.h)(0x6877970D)
I (..\source\ti\devices\msp\peripherals\hw_gpio.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_gptimer.h)(0x6877970E)
I (..\source\ti\devices\msp\peripherals\hw_i2c.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_iomux.h)(0x6877970F)
I (..\source\ti\devices\msp\peripherals\hw_mathacl.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_mcan.h)(0x68779712)
I (..\source\ti\devices\msp\peripherals\hw_oa.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_rtc.h)(0x68779713)
I (..\source\ti\devices\msp\peripherals\hw_spi.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_trng.h)(0x68779714)
I (..\source\ti\devices\msp\peripherals\hw_uart.h)(0x68779715)
I (..\source\ti\devices\msp\peripherals\hw_vref.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wuc.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\hw_wwdt.h)(0x68779716)
I (..\source\ti\devices\msp\peripherals\m0p\hw_factoryregion.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_cpuss.h)(0x6877981A)
I (..\source\ti\devices\msp\peripherals\m0p\hw_debugss.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\hw_sysctl.h)(0x6877981B)
I (..\source\ti\devices\msp\peripherals\m0p\sysctl\hw_sysctl_mspm0g1x0x_g3x0x.h)(0x687798E7)
I (..\source\ti\driverlib\driverlib.h)(0x68779674)
I (..\source\ti\driverlib\dl_adc12.h)(0x6877966C)
I (..\source\ti\driverlib\dl_common.h)(0x6877966D)
I (..\source\ti\driverlib\dl_aes.h)(0x6877966C)
I (..\source\ti\driverlib\dl_aesadv.h)(0x6877966D)
I (..\source\ti\driverlib\dl_comp.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crc.h)(0x6877966D)
I (..\source\ti\driverlib\dl_crcp.h)(0x6877966E)
I (..\source\ti\driverlib\dl_dac12.h)(0x6877966F)
I (..\source\ti\driverlib\dl_dma.h)(0x6877966E)
I (..\source\ti\driverlib\dl_flashctl.h)(0x6877966F)
I (..\source\ti\driverlib\m0p\dl_factoryregion.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_core.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_sysctl.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\sysctl\dl_sysctl_mspm0g1x0x_g3x0x.h)(0x68779718)
I (..\source\ti\driverlib\dl_gpamp.h)(0x6877966F)
I (..\source\ti\driverlib\dl_gpio.h)(0x6877966F)
I (..\source\ti\driverlib\dl_i2c.h)(0x6877966F)
I (..\source\ti\driverlib\dl_iwdt.h)(0x6877966F)
I (..\source\ti\driverlib\dl_lfss.h)(0x68779670)
I (..\source\ti\driverlib\dl_keystorectl.h)(0x68779670)
I (..\source\ti\driverlib\dl_lcd.h)(0x6877967F)
I (..\source\ti\driverlib\dl_mathacl.h)(0x68779671)
I (..\source\ti\driverlib\dl_mcan.h)(0x68779671)
I (..\source\ti\driverlib\dl_opa.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc.h)(0x68779671)
I (..\source\ti\driverlib\dl_rtc_common.h)(0x68779672)
I (..\source\ti\driverlib\dl_rtc_a.h)(0x68779672)
I (..\source\ti\driverlib\dl_scratchpad.h)(0x68779672)
I (..\source\ti\driverlib\dl_spi.h)(0x68779672)
I (..\source\ti\driverlib\dl_tamperio.h)(0x68779672)
I (..\source\ti\driverlib\dl_timera.h)(0x68779673)
I (..\source\ti\driverlib\dl_timer.h)(0x68779673)
I (..\source\ti\driverlib\dl_timerg.h)(0x68779673)
I (..\source\ti\driverlib\dl_trng.h)(0x68779673)
I (..\source\ti\driverlib\dl_uart_extend.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart.h)(0x68779674)
I (..\source\ti\driverlib\dl_uart_main.h)(0x68779674)
I (..\source\ti\driverlib\dl_vref.h)(0x68779674)
I (..\source\ti\driverlib\dl_wwdt.h)(0x68779674)
I (..\source\ti\driverlib\m0p\dl_interrupt.h)(0x687796AE)
I (..\source\ti\driverlib\m0p\dl_systick.h)(0x687796AE)
I (..\apply\wp_math.h)(0x687795CC)
I (..\apply\filter.h)(0x687795C9)
I (..\apply\pid.h)(0x687795CA)
I (..\driver\system.h)(0x687795CF)
I (..\driver\ntimer.h)(0x687795CE)
I (..\driver\npwm.h)(0x687795CE)
I (..\driver\nppm.h)(0x687795CD)
I (..\driver\nqei.h)(0x687795CE)
I (..\driver\nadc.h)(0x687795CC)
I (..\driver\nuart.h)(0x687795CE)
I (..\driver\oled.h)(0x687795CE)
I (..\driver\ssd1306.h)(0x687795CF)
I (..\driver\ni2c.h)(0x687795CD)
I (..\driver\neeprom.h)(0x687795CD)
I (..\driver\w25qxx.h)(0x687795CF)
I (..\apply\rc.h)(0x687795CA)
I (..\driver\nbutton.h)(0x687795CD)
I (..\driver\ngpio.h)(0x687795CD)
I (..\driver\us100.h)(0x687795CF)
I (..\apply\rgb.h)(0x687795CA)
I (..\driver\icm20608.h)(0x687795CC)
I (..\apply\vision.h)(0x687795CB)
I (..\apply\gray_detection.h)(0x688B16BA)
I (..\apply\sensor.h)(0x687795CB)
I (..\apply\self_balancing.h)(0x687795CB)
I (..\apply\motor_control.h)(0x687795C9)
I (..\apply\attitude_selfstable.h)(0x687795C9)
I (..\apply\ui.h)(0x687795CB)
I (..\apply\sdk.h)(0x687795CB)
I (..\apply\developer\subtask.h)(0x68779610)
I (..\apply\developer\developer_mode.h)(0x6877960F)
I (..\apply\nclink.h)(0x687795CA)
F (..\apply\Fusion\FusionAhrs.c)(0x68779610)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/fusionahrs.o -MMD)
I (..\apply\Fusion\FusionAhrs.h)(0x68779610)
I (..\apply\Fusion\FusionMath.h)(0x68779610)
I (..\apply\Fusion\FusionCompass.h)(0x68779610)
F (..\apply\Fusion\FusionCompass.c)(0x68779610)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/fusioncompass.o -MMD)
I (..\apply\Fusion\FusionCompass.h)(0x68779610)
I (..\apply\Fusion\FusionMath.h)(0x68779610)
F (..\apply\Fusion\FusionOffset.c)(0x68779610)(-xc -std=c99 --target=arm-arm-none-eabi -mcpu=cortex-m0plus -c

-fno-rtti -funsigned-char -fshort-enums -fshort-wchar

-gdwarf-4 -O0 -ffunction-sections -Wno-packed -Wno-missing-variable-declarations -Wno-missing-prototypes -Wno-missing-noreturn -Wno-sign-conversion -Wno-nonportable-include-path -Wno-reserved-id-macro -Wno-unused-macros -Wno-documentation-unknown-command -Wno-documentation -Wno-license-management -Wno-parentheses-equality -Wno-reserved-identifier -Wno-covered-switch-default -Wno-unreachable-code-break -I ../source/third_party/CMSIS/Core/Include -I ../source -I ../ -I ../driver -I ../user -I ../apply -I ../apply/developer -I ../apply/Fusion

-D__UVISION_VERSION="542" -D__MSPM0G3507__ -D__MSPM0G3507__

-o ./objects/fusionoffset.o -MMD)
I (..\apply\Fusion\FusionOffset.h)(0x68779610)
I (..\apply\Fusion\FusionMath.h)(0x68779610)
