<!doctype html public "-//w3c//dtd html 4.0 transitional//en">
<html><head>
<title>Static Call Graph - [.\Objects\ncontroller.axf]</title></head>
<body><HR>
<H1>Static Call Graph for image .\Objects\ncontroller.axf</H1><HR>
<BR><P>#&#060CALLGRAPH&#062# ARM Linker, 6230001: Last Updated: Thu Jul 31 15:13:27 2025
<BR><P>
<H3>Maximum Stack Usage =        432 bytes + Unknown(Cycles, Untraceable Function Pointers)</H3><H3>
Call chain for Maximum Stack Depth:</H3>
__rt_entry_main &rArr; main &rArr; trackless_params_init &rArr; ReadFlashParameterThree &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
<P>
<H3>
Mutually Recursive functions
</H3> <LI><a href="#[1]">NMI_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[1]">NMI_Handler</a><BR>
 <LI><a href="#[2]">HardFault_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[2]">HardFault_Handler</a><BR>
 <LI><a href="#[3]">SVC_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[3]">SVC_Handler</a><BR>
 <LI><a href="#[4]">PendSV_Handler</a>&nbsp;&nbsp;&nbsp;&rArr;&nbsp;&nbsp;&nbsp;<a href="#[4]">PendSV_Handler</a><BR>
</UL>
<P>
<H3>
Function Pointers
</H3><UL>
 <LI><a href="#[a]">ADC0_IRQHandler</a> from nadc.o(.text.ADC0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[b]">ADC1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1b]">AES_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[c]">CANFD0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[d]">DAC0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1d]">DMA_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[6]">GROUP0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[7]">GROUP1_IRQHandler</a> from nppm.o(.text.GROUP1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[2]">HardFault_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[19]">I2C0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1a]">I2C1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1]">NMI_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[4]">PendSV_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1c]">RTC_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[0]">Reset_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[e]">SPI0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[f]">SPI1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[3]">SVC_Handler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[5]">SysTick_Handler</a> from system.o(.text.SysTick_Handler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[15]">TIMA0_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[16]">TIMA1_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[13]">TIMG0_IRQHandler</a> from ntimer.o(.text.TIMG0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[18]">TIMG12_IRQHandler</a> from ntimer.o(.text.TIMG12_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[14]">TIMG6_IRQHandler</a> from ntimer.o(.text.TIMG6_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[17]">TIMG7_IRQHandler</a> from startup_mspm0g350x_uvision.o(.text) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[8]">TIMG8_IRQHandler</a> from ntimer.o(.text.TIMG8_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[12]">UART0_IRQHandler</a> from nuart.o(.text.UART0_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[10]">UART1_IRQHandler</a> from nuart.o(.text.UART1_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[11]">UART2_IRQHandler</a> from nuart.o(.text.UART2_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[9]">UART3_IRQHandler</a> from nuart.o(.text.UART3_IRQHandler) referenced from startup_mspm0g350x_uvision.o(RESET)
 <LI><a href="#[1e]">__main</a> from __main.o(!!!main) referenced from startup_mspm0g350x_uvision.o(.text)
</UL>
<P>
<H3>
Global Symbols
</H3>
<P><STRONG><a name="[1e]"></a>__main</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, __main.o(!!!main))
<BR><BR>[Calls]<UL><LI><a href="#[1f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload
<LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(.text)
</UL>
<P><STRONG><a name="[1f]"></a>__scatterload</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter))
<BR><BR>[Called By]<UL><LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[21]"></a>__scatterload_rt2</STRONG> (Thumb, 74 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[20]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry
</UL>

<P><STRONG><a name="[133]"></a>__scatterload_rt2_thumb_only</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[134]"></a>__scatterload_loop</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __scatter.o(!!!scatter), UNUSED)

<P><STRONG><a name="[135]"></a>__scatterload_null</STRONG> (Thumb, 2 bytes, Stack size unknown bytes, __scatter.o(!!handler_null), UNUSED)

<P><STRONG><a name="[136]"></a>__scatterload_zeroinit</STRONG> (Thumb, 28 bytes, Stack size unknown bytes, __scatter_zi.o(!!handler_zi), UNUSED)

<P><STRONG><a name="[25]"></a>__rt_lib_init</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit.o(.ARM.Collect$$libinit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[24]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_li
</UL>

<P><STRONG><a name="[137]"></a>__rt_lib_init_alloca_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000030))

<P><STRONG><a name="[138]"></a>__rt_lib_init_argv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000002E))

<P><STRONG><a name="[139]"></a>__rt_lib_init_atexit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001D))

<P><STRONG><a name="[13a]"></a>__rt_lib_init_clock_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000023))

<P><STRONG><a name="[13b]"></a>__rt_lib_init_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000034))

<P><STRONG><a name="[13c]"></a>__rt_lib_init_exceptions_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000032))

<P><STRONG><a name="[13d]"></a>__rt_lib_init_fp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000002))

<P><STRONG><a name="[13e]"></a>__rt_lib_init_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000021))

<P><STRONG><a name="[13f]"></a>__rt_lib_init_getenv_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000025))

<P><STRONG><a name="[140]"></a>__rt_lib_init_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000C))

<P><STRONG><a name="[141]"></a>__rt_lib_init_lc_collate_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000013))

<P><STRONG><a name="[142]"></a>__rt_lib_init_lc_ctype_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000015))

<P><STRONG><a name="[143]"></a>__rt_lib_init_lc_monetary_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000017))

<P><STRONG><a name="[144]"></a>__rt_lib_init_lc_numeric_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000019))

<P><STRONG><a name="[145]"></a>__rt_lib_init_lc_time_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001B))

<P><STRONG><a name="[146]"></a>__rt_lib_init_preinit_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000006))

<P><STRONG><a name="[147]"></a>__rt_lib_init_rand_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000010))

<P><STRONG><a name="[148]"></a>__rt_lib_init_relocate_pie_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000004))

<P><STRONG><a name="[149]"></a>__rt_lib_init_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000035))

<P><STRONG><a name="[14a]"></a>__rt_lib_init_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000001F))

<P><STRONG><a name="[14b]"></a>__rt_lib_init_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$00000027))

<P><STRONG><a name="[14c]"></a>__rt_lib_init_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libinit2.o(.ARM.Collect$$libinit$$0000000E))

<P><STRONG><a name="[2a]"></a>__rt_lib_shutdown</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown.o(.ARM.Collect$$libshutdown$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[29]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_ls
</UL>

<P><STRONG><a name="[14d]"></a>__rt_lib_shutdown_cpp_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000002))

<P><STRONG><a name="[14e]"></a>__rt_lib_shutdown_fp_trap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000007))

<P><STRONG><a name="[14f]"></a>__rt_lib_shutdown_heap_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000F))

<P><STRONG><a name="[150]"></a>__rt_lib_shutdown_return</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000010))

<P><STRONG><a name="[151]"></a>__rt_lib_shutdown_signal_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000A))

<P><STRONG><a name="[152]"></a>__rt_lib_shutdown_stdio_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$00000004))

<P><STRONG><a name="[153]"></a>__rt_lib_shutdown_user_alloc_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C))

<P><STRONG><a name="[20]"></a>__rt_entry</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry.o(.ARM.Collect$$rtentry$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[21]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__scatterload_rt2
<LI><a href="#[1e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__main
</UL>

<P><STRONG><a name="[154]"></a>__rt_entry_presh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000002))

<P><STRONG><a name="[22]"></a>__rt_entry_sh</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry4.o(.ARM.Collect$$rtentry$$00000004))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = __rt_entry_sh &rArr; __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[24]"></a>__rt_entry_li</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000A))
<BR><BR>[Calls]<UL><LI><a href="#[25]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_init
</UL>

<P><STRONG><a name="[155]"></a>__rt_entry_postsh_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$00000009))

<P><STRONG><a name="[26]"></a>__rt_entry_main</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000D))
<BR><BR>[Stack]<UL><LI>Max Depth = 432 + Unknown Stack Size
<LI>Call Chain = __rt_entry_main &rArr; main &rArr; trackless_params_init &rArr; ReadFlashParameterThree &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[156]"></a>__rt_entry_postli_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __rtentry2.o(.ARM.Collect$$rtentry$$0000000C))

<P><STRONG><a name="[39]"></a>__rt_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit.o(.ARM.Collect$$rtexit$$00000000))
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>

<P><STRONG><a name="[29]"></a>__rt_exit_ls</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000003))
<BR><BR>[Calls]<UL><LI><a href="#[2a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_lib_shutdown
</UL>

<P><STRONG><a name="[157]"></a>__rt_exit_prels_1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000002))

<P><STRONG><a name="[2b]"></a>__rt_exit_exit</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, rtexit2.o(.ARM.Collect$$rtexit$$00000004))
<BR><BR>[Calls]<UL><LI><a href="#[2c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_sys_exit
</UL>

<P><STRONG><a name="[109]"></a>__aeabi_memcpy4</STRONG> (Thumb, 56 bytes, Stack size 0 bytes, rt_memcpy.o(.emb_text))
<BR><BR>[Called By]<UL><LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rc_range_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctrl_params_init
</UL>

<P><STRONG><a name="[158]"></a>__aeabi_memcpy8</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, rt_memcpy.o(.emb_text), UNUSED)

<P><STRONG><a name="[0]"></a>Reset_Handler</STRONG> (Thumb, 4 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1]"></a>NMI_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NMI_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2]"></a>HardFault_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;HardFault_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[3]"></a>SVC_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SVC_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[4]"></a>PendSV_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PendSV_Handler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[b]"></a>ADC1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1b]"></a>AES_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[c]"></a>CANFD0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[d]"></a>DAC0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1d]"></a>DMA_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[2d]"></a>Default_Handler</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>
<BR>[Called By]<UL><LI><a href="#[2d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Default_Handler
</UL>

<P><STRONG><a name="[6]"></a>GROUP0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[19]"></a>I2C0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1a]"></a>I2C1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[1c]"></a>RTC_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[e]"></a>SPI0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f]"></a>SPI1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[15]"></a>TIMA0_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[16]"></a>TIMA1_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[17]"></a>TIMG7_IRQHandler</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[37]"></a>__user_initial_stackheap</STRONG> (Thumb, 10 bytes, Stack size 0 bytes, startup_mspm0g350x_uvision.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[87]"></a>__aeabi_uidiv</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, aeabi_sdiv.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
<LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bling_set
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[159]"></a>__aeabi_uidivmod</STRONG> (Thumb, 20 bytes, Stack size 0 bytes, aeabi_sdiv.o(.text), UNUSED)

<P><STRONG><a name="[15a]"></a>__aeabi_idiv</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, aeabi_sdiv.o(.text), UNUSED)

<P><STRONG><a name="[15b]"></a>__aeabi_idivmod</STRONG> (Thumb, 338 bytes, Stack size 8 bytes, aeabi_sdiv.o(.text), UNUSED)

<P><STRONG><a name="[15c]"></a>__use_two_region_memory</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[15d]"></a>__rt_heap_escrow$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[15e]"></a>__rt_heap_expand$2region</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, heapauxi.o(.text), UNUSED)

<P><STRONG><a name="[103]"></a>__aeabi_f2d</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, f2d.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[15f]"></a>__extendsfdf2</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, f2d.o(.text), UNUSED)

<P><STRONG><a name="[160]"></a>_f2d</STRONG> (Thumb, 80 bytes, Stack size 0 bytes, f2d.o(.text), UNUSED)

<P><STRONG><a name="[90]"></a>__aeabi_fdiv</STRONG> (Thumb, 0 bytes, Stack size 20 bytes, fdiv.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = __aeabi_fdiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[161]"></a>__divsf3</STRONG> (Thumb, 0 bytes, Stack size 20 bytes, fdiv.o(.text), UNUSED)

<P><STRONG><a name="[2f]"></a>_fdiv</STRONG> (Thumb, 334 bytes, Stack size 20 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[2e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_frdiv
</UL>

<P><STRONG><a name="[2e]"></a>_frdiv</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, fdiv.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[2f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fdiv
</UL>

<P><STRONG><a name="[fd]"></a>__aeabi_f2iz</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, ffixi.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rangefinder_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rc_range_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trackless_params_init
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bluetooth_app_prase
</UL>

<P><STRONG><a name="[162]"></a>_ffix</STRONG> (Thumb, 76 bytes, Stack size 8 bytes, ffixi.o(.text), UNUSED)

<P><STRONG><a name="[31]"></a>__aeabi_i2f_normalise</STRONG> (Thumb, 72 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
</UL>

<P><STRONG><a name="[30]"></a>__aeabi_i2f</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rc_range_init
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[163]"></a>_fflt</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[32]"></a>__aeabi_ui2f</STRONG> (Thumb, 6 bytes, Stack size 0 bytes, fflti.o(.text))
<BR><BR>[Calls]<UL><LI><a href="#[31]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f_normalise
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[164]"></a>_ffltu</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, fflti.o(.text), UNUSED)

<P><STRONG><a name="[33]"></a>__fpl_fcmp_InfNaN</STRONG> (Thumb, 96 bytes, Stack size 8 bytes, fcmpin.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
<LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
<LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[165]"></a>__user_libspace</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[36]"></a>__user_perproc_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[23]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_setup_stackheap
</UL>

<P><STRONG><a name="[166]"></a>__user_perthread_libspace</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, libspace.o(.text), UNUSED)

<P><STRONG><a name="[23]"></a>__user_setup_stackheap</STRONG> (Thumb, 62 bytes, Stack size 8 bytes, sys_stackheap_outer.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __user_setup_stackheap
</UL>
<BR>[Calls]<UL><LI><a href="#[37]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_initial_stackheap
<LI><a href="#[36]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__user_perproc_libspace
</UL>
<BR>[Called By]<UL><LI><a href="#[22]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_sh
</UL>

<P><STRONG><a name="[28]"></a>exit</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, exit.o(.text))
<BR><BR>[Stack]<UL><LI>Max Depth = 8 + Unknown Stack Size
<LI>Call Chain = exit
</UL>
<BR>[Calls]<UL><LI><a href="#[39]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit
<LI><a href="#[38]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_call_atexit_fns (Weak Reference)
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[34]"></a>__fpl_cmpreturn</STRONG> (Thumb, 46 bytes, Stack size 0 bytes, cmpret.o(.text), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
<LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[35]"></a>__fpl_fcheck_NaN2</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, fnan2.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[3a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_return_NaN
</UL>
<BR>[Called By]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>

<P><STRONG><a name="[3a]"></a>__fpl_return_NaN</STRONG> (Thumb, 94 bytes, Stack size 8 bytes, retnan.o(.text), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[34]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_cmpreturn
</UL>
<BR>[Called By]<UL><LI><a href="#[35]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcheck_NaN2
</UL>

<P><STRONG><a name="[2c]"></a>_sys_exit</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, sys_exit.o(.text))
<BR><BR>[Called By]<UL><LI><a href="#[2b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_exit_exit
</UL>

<P><STRONG><a name="[167]"></a>__I$use$semihosting</STRONG> (Thumb, 0 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[168]"></a>__use_no_semihosting_swi</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, use_no_semi.o(.text), UNUSED)

<P><STRONG><a name="[169]"></a>__decompress</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[16a]"></a>__decompress0</STRONG> (Thumb, 58 bytes, Stack size unknown bytes, __dczerorl.o(.text), UNUSED)

<P><STRONG><a name="[16b]"></a>__semihosting_library_function</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, indicate_semi.o(.text), UNUSED)

<P><STRONG><a name="[a]"></a>ADC0_IRQHandler</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nadc.o(.text.ADC0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = ADC0_IRQHandler &rArr; DL_ADC12_getPendingInterrupt
</UL>
<BR>[Calls]<UL><LI><a href="#[3b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_getPendingInterrupt
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11c]"></a>Button_Init</STRONG> (Thumb, 104 bytes, Stack size 8 bytes, nbutton.o(.text.Button_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = Button_Init
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[85]"></a>Byte2Float</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, nclink.o(.text.Byte2Float))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = Byte2Float
</UL>
<BR>[Called By]<UL><LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bluetooth_app_prase
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
</UL>

<P><STRONG><a name="[3c]"></a>DL_ADC12_setClockConfig</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_ADC12_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_ADC12_setClockConfig &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[e8]"></a>DL_Common_delayCycles</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_common.o(.text.DL_Common_delayCycles))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Common_delayCycles
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[40]"></a>DL_DMA_initChannel</STRONG> (Thumb, 70 bytes, Stack size 56 bytes, dl_dma.o(.text.DL_DMA_initChannel))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setTrigger
<LI><a href="#[41]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_configTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>

<P><STRONG><a name="[45]"></a>DL_I2C_setClockConfig</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, dl_i2c.o(.text.DL_I2C_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_I2C_setClockConfig &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[46]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[49]"></a>DL_SPI_init</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, dl_spi.o(.text.DL_SPI_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SPI_init &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[4a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[c0]"></a>DL_SPI_setClockConfig</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, dl_spi.o(.text.DL_SPI_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[4d]"></a>DL_SYSCTL_configSYSPLL</STRONG> (Thumb, 208 bytes, Stack size 32 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_configSYSPLL))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_SYSCTL_configSYSPLL &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[52]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_CORE_configInstruction
<LI><a href="#[51]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_CORE_getInstructionConfig
<LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_getClockStatus
<LI><a href="#[4e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableSYSPLL
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[55]"></a>DL_SYSCTL_setHFCLKSourceHFXTParams</STRONG> (Thumb, 96 bytes, Stack size 32 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFCLKSourceHFXTParams))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = DL_SYSCTL_setHFCLKSourceHFXTParams &rArr; DL_SYSCTL_setHFXTStartupTime &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[5a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFCLKStartupMonitor
<LI><a href="#[59]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_enableHFCLKStartupMonitor
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFXTStartupTime
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFXTFrequencyRange
<LI><a href="#[56]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFXT
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_getClockStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5e]"></a>DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK &rArr; DL_SYSCTL_setHSCLKSource
</UL>
<BR>[Calls]<UL><LI><a href="#[5f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHSCLKSource
<LI><a href="#[4f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_getClockStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[60]"></a>DL_TimerA_initPWMMode</STRONG> (Thumb, 234 bytes, Stack size 56 bytes, dl_timer.o(.text.DL_TimerA_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 152<LI>Call Chain = DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[65]"></a>DL_Timer_initPWMMode</STRONG> (Thumb, 260 bytes, Stack size 48 bytes, dl_timer.o(.text.DL_Timer_initPWMMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setLoadValue
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
</UL>

<P><STRONG><a name="[69]"></a>DL_Timer_initTimerMode</STRONG> (Thumb, 252 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_initTimerMode))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
<LI><a href="#[66]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setLoadValue
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
</UL>

<P><STRONG><a name="[6b]"></a>DL_Timer_setCaptCompUpdateMethod</STRONG> (Thumb, 48 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_setCaptCompUpdateMethod))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_Timer_setCaptCompUpdateMethod &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[61]"></a>DL_Timer_setCaptureCompareAction</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareAction))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_Timer_setCaptureCompareAction &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
</UL>

<P><STRONG><a name="[62]"></a>DL_Timer_setCaptureCompareCtl</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
</UL>

<P><STRONG><a name="[64]"></a>DL_Timer_setCaptureCompareInput</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
</UL>

<P><STRONG><a name="[63]"></a>DL_Timer_setCaptureCompareOutCtl</STRONG> (Thumb, 60 bytes, Stack size 36 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareOutCtl))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = DL_Timer_setCaptureCompareOutCtl
</UL>
<BR>[Called By]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[6a]"></a>DL_Timer_setCaptureCompareValue</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_setCaptureCompareValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Timer_setCaptureCompareValue
</UL>
<BR>[Called By]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[ba]"></a>DL_Timer_setClockConfig</STRONG> (Thumb, 44 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[6c]"></a>DL_UART_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_UART_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_init &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[6e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
<LI><a href="#[6d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[d2]"></a>DL_UART_setClockConfig</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, dl_uart.o(.text.DL_UART_setClockConfig))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_setClockConfig
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[73]"></a>Delay_Ms</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, system.o(.text.Delay_Ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = Delay_Ms &rArr; delay_ms &rArr; delay &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
</UL>

<P><STRONG><a name="[11b]"></a>Encoder_Init</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, nqei.o(.text.Encoder_Init))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[75]"></a>FastSqrt</STRONG> (Thumb, 26 bytes, Stack size 16 bytes, wp_math.o(.text.FastSqrt))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = FastSqrt &rArr; FastSqrtI &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrtI
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[76]"></a>FastSqrtI</STRONG> (Thumb, 64 bytes, Stack size 24 bytes, wp_math.o(.text.FastSqrtI))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = FastSqrtI &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrt
</UL>

<P><STRONG><a name="[7]"></a>GROUP1_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 8 bytes, nppm.o(.text.GROUP1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = GROUP1_IRQHandler &rArr; QEI1_IRQHandler
</UL>
<BR>[Calls]<UL><LI><a href="#[7e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Interrupt_clearGroup
<LI><a href="#[7c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[7a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_getEnabledInterruptStatus
<LI><a href="#[79]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Interrupt_getStatusGroup
<LI><a href="#[7d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEI1_IRQHandler
<LI><a href="#[7b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;QEI0_IRQHandler
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[7f]"></a>LCD_CLS</STRONG> (Thumb, 96 bytes, Stack size 16 bytes, oled.o(.text.LCD_CLS))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = LCD_CLS &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[82]"></a>NCLink_Data_Prase_Prepare_Lite</STRONG> (Thumb, 396 bytes, Stack size 16 bytes, nclink.o(.text.NCLink_Data_Prase_Prepare_Lite))
<BR><BR>[Stack]<UL><LI>Max Depth = 144<LI>Call Chain = NCLink_Data_Prase_Prepare_Lite &rArr; NCLink_Data_Prase_Process_Lite &rArr; Pilot_Status_Tick &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
</UL>
<BR>[Called By]<UL><LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[83]"></a>NCLink_Data_Prase_Process_Lite</STRONG> (Thumb, 824 bytes, Stack size 72 bytes, nclink.o(.text.NCLink_Data_Prase_Process_Lite))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = NCLink_Data_Prase_Process_Lite &rArr; Pilot_Status_Tick &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte2Float
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pilot_Status_Tick
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Prepare_Lite
</UL>

<P><STRONG><a name="[88]"></a>OLED_Init</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, oled.o(.text.OLED_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 184<LI>Call Chain = OLED_Init &rArr; ssd1306_begin &rArr; ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
<LI><a href="#[8a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[89]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[80]"></a>OLED_WrCmd</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(.text.OLED_WrCmd))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
<LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
</UL>

<P><STRONG><a name="[81]"></a>OLED_WrDat</STRONG> (Thumb, 42 bytes, Stack size 16 bytes, oled.o(.text.OLED_WrDat))
<BR><BR>[Stack]<UL><LI>Max Depth = 104<LI>Call Chain = OLED_WrDat &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>
<BR>[Called By]<UL><LI><a href="#[7f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;LCD_CLS
</UL>

<P><STRONG><a name="[8f]"></a>Openmv_Data_Receive_Anl_1</STRONG> (Thumb, 1988 bytes, Stack size 80 bytes, vision.o(.text.Openmv_Data_Receive_Anl_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = Openmv_Data_Receive_Anl_1 &rArr; FastSqrt &rArr; FastSqrtI &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrt
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDK_Data_Receive_Prepare_1
</UL>

<P><STRONG><a name="[92]"></a>PPM_Init</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, nppm.o(.text.PPM_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = PPM_Init &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[94]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
<LI><a href="#[93]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[84]"></a>Pilot_Status_Tick</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, nclink.o(.text.Pilot_Status_Tick))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = Pilot_Status_Tick &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bling_set
</UL>
<BR>[Called By]<UL><LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
</UL>

<P><STRONG><a name="[7b]"></a>QEI0_IRQHandler</STRONG> (Thumb, 144 bytes, Stack size 12 bytes, nqei.o(.text.QEI0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = QEI0_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[7d]"></a>QEI1_IRQHandler</STRONG> (Thumb, 144 bytes, Stack size 12 bytes, nqei.o(.text.QEI1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = QEI1_IRQHandler
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[96]"></a>ReadFlashParameterOne</STRONG> (Thumb, 32 bytes, Stack size 24 bytes, neeprom.o(.text.ReadFlashParameterOne))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = ReadFlashParameterOne &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read_f
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rangefinder_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trackless_params_init
</UL>

<P><STRONG><a name="[98]"></a>ReadFlashParameterThree</STRONG> (Thumb, 72 bytes, Stack size 40 bytes, neeprom.o(.text.ReadFlashParameterThree))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = ReadFlashParameterThree &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read_f
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trackless_params_init
</UL>

<P><STRONG><a name="[99]"></a>SDK_Data_Receive_Prepare_1</STRONG> (Thumb, 400 bytes, Stack size 32 bytes, vision.o(.text.SDK_Data_Receive_Prepare_1))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = SDK_Data_Receive_Prepare_1 &rArr; Openmv_Data_Receive_Anl_1 &rArr; FastSqrt &rArr; FastSqrtI &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>
<BR>[Called By]<UL><LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
</UL>

<P><STRONG><a name="[9a]"></a>SYSCFG_DL_ADC12_0_init</STRONG> (Thumb, 96 bytes, Stack size 40 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_ADC12_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = SYSCFG_DL_ADC12_0_init &rArr; DL_ADC12_setClockConfig &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
<LI><a href="#[a0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enableConversions
<LI><a href="#[9e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enableInterrupt
<LI><a href="#[9d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_clearInterruptStatus
<LI><a href="#[9c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setSampleTime0
<LI><a href="#[9b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_configConversionMem
<LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setStartAddress
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a1]"></a>SYSCFG_DL_DMA_CH0_init</STRONG> (Thumb, 16 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_CH0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 112<LI>Call Chain = SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>
<BR>[Called By]<UL><LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
</UL>

<P><STRONG><a name="[a2]"></a>SYSCFG_DL_DMA_init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_DMA_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SYSCFG_DL_DMA_init &rArr; SYSCFG_DL_DMA_CH0_init &rArr; DL_DMA_initChannel &rArr; DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[a1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_CH0_init
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[a3]"></a>SYSCFG_DL_GPIO_init</STRONG> (Thumb, 652 bytes, Stack size 72 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_GPIO_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = SYSCFG_DL_GPIO_init &rArr; DL_GPIO_initDigitalOutputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[b2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableInterrupt
<LI><a href="#[b1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearInterruptStatus
<LI><a href="#[b0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setLowerPinsPolarity
<LI><a href="#[af]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
<LI><a href="#[ae]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[ad]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutputFeatures
<LI><a href="#[ac]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
<LI><a href="#[ab]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInputFeatures
<LI><a href="#[aa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInput
<LI><a href="#[a9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunction
<LI><a href="#[a8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableHiZ
<LI><a href="#[a7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralInputFunctionFeatures
<LI><a href="#[a6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[a5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralOutputFunction
<LI><a href="#[a4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initPeripheralAnalogFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b3]"></a>SYSCFG_DL_I2C_0_init</STRONG> (Thumb, 80 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_I2C_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = SYSCFG_DL_I2C_0_init &rArr; DL_I2C_setControllerRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setClockConfig
<LI><a href="#[b8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableController
<LI><a href="#[b7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableControllerClockStretching
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerRXFIFOThreshold
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerTXFIFOThreshold
<LI><a href="#[b6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setTimerPeriod
<LI><a href="#[b5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_resetControllerTransfer
<LI><a href="#[b4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enableAnalogGlitchFilter
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setAnalogGlitchFilterPulseWidth
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[b9]"></a>SYSCFG_DL_PWM_0_init</STRONG> (Thumb, 192 bytes, Stack size 40 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bd]"></a>SYSCFG_DL_PWM_1_init</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 176<LI>Call Chain = SYSCFG_DL_PWM_1_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[60]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_TimerA_initPWMMode
<LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[be]"></a>SYSCFG_DL_PWM_2_init</STRONG> (Thumb, 116 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_PWM_2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = SYSCFG_DL_PWM_2_init &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[63]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareOutCtl
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[6a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareValue
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[bc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCCPDirection
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[bf]"></a>SYSCFG_DL_SPI_0_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SPI_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_SPI_0_init &rArr; DL_SPI_setFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[c0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setClockConfig
<LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_init
<LI><a href="#[c1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_enable
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setFIFOThreshold
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setBitRateSerialClockDivider
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[c2]"></a>SYSCFG_DL_SYSCTL_CLK_init</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_CLK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = SYSCFG_DL_SYSCTL_CLK_init
</UL>
<BR>[Calls]<UL><LI><a href="#[c3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_getClockStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[c4]"></a>SYSCFG_DL_SYSCTL_init</STRONG> (Thumb, 100 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSCTL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SYSCFG_DL_SYSCTL_init &rArr; DL_SYSCTL_setHFCLKSourceHFXTParams &rArr; DL_SYSCTL_setHFXTStartupTime &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setMFPCLKSource
<LI><a href="#[c9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_enableMFPCLK
<LI><a href="#[c8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_enableMFCLK
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKDividerForMFPCLK
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[c7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableSYSPLL
<LI><a href="#[c6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_disableHFXT
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
<LI><a href="#[c5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[ca]"></a>SYSCFG_DL_SYSTICK_init</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_SYSTICK_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_SYSTICK_init &rArr; SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[cc]"></a>SYSCFG_DL_TIMER_1_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_TIMER_1_init &rArr; DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[ce]"></a>SYSCFG_DL_TIMER_2_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_TIMER_2_init &rArr; DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[cf]"></a>SYSCFG_DL_TIMER_G12_init</STRONG> (Thumb, 52 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G12_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_TIMER_G12_init &rArr; DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d0]"></a>SYSCFG_DL_TIMER_G8_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_TIMER_G8_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = SYSCFG_DL_TIMER_G8_init &rArr; DL_Timer_initTimerMode &rArr; DL_Timer_setCaptureCompareCtl &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
<LI><a href="#[ba]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setClockConfig
<LI><a href="#[cd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableInterrupt
<LI><a href="#[bb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enableClock
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d1]"></a>SYSCFG_DL_UART_0_init</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_0_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_0_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d5]"></a>SYSCFG_DL_UART_1_init</STRONG> (Thumb, 60 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_1_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_1_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d6]"></a>SYSCFG_DL_UART_2_init</STRONG> (Thumb, 92 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_2_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_2_init &rArr; DL_UART_setTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setTXFIFOThreshold
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setRXFIFOThreshold
<LI><a href="#[d8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableFIFOs
<LI><a href="#[d7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableDMATransmitEvent
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[d9]"></a>SYSCFG_DL_UART_3_init</STRONG> (Thumb, 64 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_UART_3_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = SYSCFG_DL_UART_3_init &rArr; DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[d2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setClockConfig
<LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
<LI><a href="#[d4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enable
<LI><a href="#[d3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enableInterrupt
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[da]"></a>SYSCFG_DL_init</STRONG> (Thumb, 120 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 200<LI>Call Chain = SYSCFG_DL_init &rArr; SYSCFG_DL_PWM_0_init &rArr; DL_TimerA_initPWMMode &rArr; DL_Timer_initPWMMode &rArr; DL_Timer_setCaptureCompareInput &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_CLK_init
<LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
<LI><a href="#[a2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_DMA_init
<LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
<LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
<LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
<LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[db]"></a>SYSCFG_DL_initPower</STRONG> (Thumb, 240 bytes, Stack size 72 bytes, ti_msp_dl_config.o(.text.SYSCFG_DL_initPower))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = SYSCFG_DL_initPower &rArr; DL_Common_delayCycles
</UL>
<BR>[Calls]<UL><LI><a href="#[e8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_delayCycles
<LI><a href="#[e7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_enablePower
<LI><a href="#[e6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_enablePower
<LI><a href="#[e5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_enablePower
<LI><a href="#[e4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_enablePower
<LI><a href="#[e3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_enablePower
<LI><a href="#[e2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enablePower
<LI><a href="#[e1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_reset
<LI><a href="#[e0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_reset
<LI><a href="#[df]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_reset
<LI><a href="#[de]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_reset
<LI><a href="#[dd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_reset
<LI><a href="#[dc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
</UL>

<P><STRONG><a name="[5]"></a>SysTick_Handler</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, system.o(.text.SysTick_Handler))
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[13]"></a>TIMG0_IRQHandler</STRONG> (Thumb, 68 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG0_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[ec]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_togglePins
<LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[eb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;maple_duty_200hz
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[18]"></a>TIMG12_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG12_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG12_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;duty_1000hz
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[14]"></a>TIMG6_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG6_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG6_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[ee]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;duty_100hz
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[8]"></a>TIMG8_IRQHandler</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, ntimer.o(.text.TIMG8_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = TIMG8_IRQHandler &rArr; get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[e9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_getPendingInterrupt
<LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[ef]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;duty_10hz
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[12]"></a>UART0_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, nuart.o(.text.UART0_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 168<LI>Call Chain = UART0_IRQHandler &rArr; NCLink_Data_Prase_Prepare_Lite &rArr; NCLink_Data_Prase_Process_Lite &rArr; Pilot_Status_Tick &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[82]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Prepare_Lite
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getEnabledInterruptStatus
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_clearInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[10]"></a>UART1_IRQHandler</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, nuart.o(.text.UART1_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 192<LI>Call Chain = UART1_IRQHandler &rArr; SDK_Data_Receive_Prepare_1 &rArr; Openmv_Data_Receive_Anl_1 &rArr; FastSqrt &rArr; FastSqrtI &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[99]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SDK_Data_Receive_Prepare_1
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getEnabledInterruptStatus
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_clearInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[11]"></a>UART2_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 24 bytes, nuart.o(.text.UART2_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = UART2_IRQHandler &rArr; bluetooth_app_prase &rArr; Byte2Float
</UL>
<BR>[Calls]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[f3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bluetooth_app_prase
<LI><a href="#[f4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getPendingInterrupt
<LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getEnabledInterruptStatus
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_clearInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[9]"></a>UART3_IRQHandler</STRONG> (Thumb, 84 bytes, Stack size 16 bytes, nuart.o(.text.UART3_IRQHandler))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = UART3_IRQHandler &rArr; DL_UART_getEnabledInterruptStatus
</UL>
<BR>[Calls]<UL><LI><a href="#[f1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_receiveData
<LI><a href="#[f0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_getEnabledInterruptStatus
<LI><a href="#[f2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_clearInterruptStatus
</UL>
<BR>[Address Reference Count : 1]<UL><LI> startup_mspm0g350x_uvision.o(RESET)
</UL>
<P><STRONG><a name="[f5]"></a>W25Q64_read</STRONG> (Thumb, 116 bytes, Stack size 32 bytes, w25qxx.o(.text.W25Q64_read))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[97]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_Read_f
</UL>

<P><STRONG><a name="[f9]"></a>W25Q64_readID</STRONG> (Thumb, 104 bytes, Stack size 32 bytes, w25qxx.o(.text.W25Q64_readID))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = W25Q64_readID &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
</UL>

<P><STRONG><a name="[fa]"></a>W25QXX_PowerDown</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, w25qxx.o(.text.W25QXX_PowerDown))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = W25QXX_PowerDown &rArr; delay_us &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
</UL>

<P><STRONG><a name="[97]"></a>W25QXX_Read_f</STRONG> (Thumb, 78 bytes, Stack size 32 bytes, w25qxx.o(.text.W25QXX_Read_f))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_read
</UL>
<BR>[Called By]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadFlashParameterThree
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadFlashParameterOne
</UL>

<P><STRONG><a name="[fc]"></a>W25QXX_WAKEUP</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, w25qxx.o(.text.W25QXX_WAKEUP))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = W25QXX_WAKEUP &rArr; delay_us &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
<LI><a href="#[f6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
</UL>

<P><STRONG><a name="[95]"></a>bling_set</STRONG> (Thumb, 70 bytes, Stack size 40 bytes, rgb.o(.text.bling_set))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_start_bling
<LI><a href="#[84]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Pilot_Status_Tick
</UL>

<P><STRONG><a name="[f3]"></a>bluetooth_app_prase</STRONG> (Thumb, 488 bytes, Stack size 40 bytes, nclink.o(.text.bluetooth_app_prase))
<BR><BR>[Stack]<UL><LI>Max Depth = 56<LI>Call Chain = bluetooth_app_prase &rArr; Byte2Float
</UL>
<BR>[Calls]<UL><LI><a href="#[85]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Byte2Float
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
</UL>

<P><STRONG><a name="[8d]"></a>bsp_analog_i2c_send_byte</STRONG> (Thumb, 128 bytes, Stack size 24 bytes, oled.o(.text.bsp_analog_i2c_send_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 88<LI>Call Chain = bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[8c]"></a>bsp_analog_i2c_start</STRONG> (Thumb, 56 bytes, Stack size 16 bytes, oled.o(.text.bsp_analog_i2c_start))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_analog_i2c_start &rArr; i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
</UL>

<P><STRONG><a name="[8e]"></a>bsp_analog_i2c_stop</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, oled.o(.text.bsp_analog_i2c_stop))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = bsp_analog_i2c_stop &rArr; i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
<LI><a href="#[81]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrDat
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
</UL>

<P><STRONG><a name="[100]"></a>bsp_analog_i2c_wait_ack</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, oled.o(.text.bsp_analog_i2c_wait_ack))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_in
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
<LI><a href="#[ff]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
</UL>

<P><STRONG><a name="[86]"></a>constrain_float</STRONG> (Thumb, 130 bytes, Stack size 32 bytes, wp_math.o(.text.constrain_float))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = constrain_float &rArr; __ARM_isnan
</UL>
<BR>[Calls]<UL><LI><a href="#[104]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnan
<LI><a href="#[102]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnanf
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
<LI><a href="#[103]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2d
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[83]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;NCLink_Data_Prase_Process_Lite
</UL>

<P><STRONG><a name="[107]"></a>ctrl_params_init</STRONG> (Thumb, 272 bytes, Stack size 88 bytes, sdk.o(.text.ctrl_params_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 136<LI>Call Chain = ctrl_params_init &rArr; pid_control_init
</UL>
<BR>[Calls]<UL><LI><a href="#[108]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_init
<LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[10a]"></a>delay</STRONG> (Thumb, 22 bytes, Stack size 16 bytes, system.o(.text.delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = delay &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>
<BR>[Called By]<UL><LI><a href="#[74]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_ms
</UL>

<P><STRONG><a name="[10b]"></a>delayMicroseconds</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, system.o(.text.delayMicroseconds))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
</UL>
<BR>[Called By]<UL><LI><a href="#[fb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay_us
<LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>

<P><STRONG><a name="[74]"></a>delay_ms</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, system.o(.text.delay_ms))
<BR><BR>[Stack]<UL><LI>Max Depth = 64<LI>Call Chain = delay_ms &rArr; delay &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[10a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delay
</UL>
<BR>[Called By]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[fb]"></a>delay_us</STRONG> (Thumb, 16 bytes, Stack size 16 bytes, system.o(.text.delay_us))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = delay_us &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>
<BR>[Called By]<UL><LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
</UL>

<P><STRONG><a name="[ed]"></a>duty_1000hz</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, main.o(.text.duty_1000hz))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = duty_1000hz &rArr; gpio_input_check_channel_5
</UL>
<BR>[Calls]<UL><LI><a href="#[10d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gpio_input_check_channel_5
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG12_IRQHandler
</UL>

<P><STRONG><a name="[ee]"></a>duty_100hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(.text.duty_100hz))
<BR><BR>[Called By]<UL><LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG6_IRQHandler
</UL>

<P><STRONG><a name="[ef]"></a>duty_10hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(.text.duty_10hz))
<BR><BR>[Called By]<UL><LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG8_IRQHandler
</UL>

<P><STRONG><a name="[ea]"></a>get_systime</STRONG> (Thumb, 72 bytes, Stack size 16 bytes, system.o(.text.get_systime))
<BR><BR>[Stack]<UL><LI>Max Depth = 36<LI>Call Chain = get_systime &rArr; __aeabi_fdiv
</UL>
<BR>[Calls]<UL><LI><a href="#[10c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;micros
<LI><a href="#[32]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_ui2f
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
<LI><a href="#[90]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fdiv
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG12_IRQHandler
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG8_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG6_IRQHandler
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
</UL>

<P><STRONG><a name="[10d]"></a>gpio_input_check_channel_5</STRONG> (Thumb, 476 bytes, Stack size 20 bytes, gray_detection.o(.text.gpio_input_check_channel_5))
<BR><BR>[Stack]<UL><LI>Max Depth = 20<LI>Call Chain = gpio_input_check_channel_5
</UL>
<BR>[Called By]<UL><LI><a href="#[ed]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;duty_1000hz
</UL>

<P><STRONG><a name="[10e]"></a>gray_turn_control_200hz</STRONG> (Thumb, 196 bytes, Stack size 48 bytes, gray_detection.o(.text.gray_turn_control_200hz))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = gray_turn_control_200hz &rArr; pid_control_run &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[105]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpge
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[101]"></a>i2c_sda_in</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, oled.o(.text.i2c_sda_in))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[110]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
</UL>

<P><STRONG><a name="[fe]"></a>i2c_sda_out</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.i2c_sda_out))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = i2c_sda_out &rArr; DL_GPIO_enableOutput
</UL>
<BR>[Calls]<UL><LI><a href="#[112]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_enableOutput
<LI><a href="#[111]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[27]"></a>main</STRONG> (Thumb, 308 bytes, Stack size 40 bytes, main.o(.text.main))
<BR><BR>[Stack]<UL><LI>Max Depth = 432<LI>Call Chain = main &rArr; trackless_params_init &rArr; ReadFlashParameterThree &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_Init
<LI><a href="#[11c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Button_Init
<LI><a href="#[11b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Encoder_Init
<LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_init
<LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rangefinder_init
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rc_range_init
<LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simulation_pwm_init
<LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trackless_params_init
<LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctrl_params_init
<LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
<LI><a href="#[114]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nGPIO_Init
<LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nADC_Init
<LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
<LI><a href="#[da]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_init
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[26]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__rt_entry_main
</UL>

<P><STRONG><a name="[eb]"></a>maple_duty_200hz</STRONG> (Thumb, 2 bytes, Stack size 0 bytes, main.o(.text.maple_duty_200hz))
<BR><BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[10c]"></a>micros</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, system.o(.text.micros))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = micros
</UL>
<BR>[Calls]<UL><LI><a href="#[87]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_uidiv
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[10b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;delayMicroseconds
</UL>

<P><STRONG><a name="[113]"></a>nADC_Init</STRONG> (Thumb, 10 bytes, Stack size 8 bytes, nadc.o(.text.nADC_Init))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = nADC_Init &rArr; __NVIC_EnableIRQ
</UL>
<BR>[Calls]<UL><LI><a href="#[11e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[114]"></a>nGPIO_Init</STRONG> (Thumb, 32 bytes, Stack size 0 bytes, ngpio.o(.text.nGPIO_Init))
<BR><BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[108]"></a>pid_control_init</STRONG> (Thumb, 130 bytes, Stack size 48 bytes, pid.o(.text.pid_control_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = pid_control_init
</UL>
<BR>[Called By]<UL><LI><a href="#[107]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ctrl_params_init
</UL>

<P><STRONG><a name="[10f]"></a>pid_control_run</STRONG> (Thumb, 504 bytes, Stack size 56 bytes, pid.o(.text.pid_control_run))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = pid_control_run &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[11f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmplt
<LI><a href="#[106]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmple
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
</UL>

<P><STRONG><a name="[119]"></a>rangefinder_init</STRONG> (Thumb, 68 bytes, Stack size 16 bytes, us100.o(.text.rangefinder_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 128<LI>Call Chain = rangefinder_init &rArr; ReadFlashParameterOne &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[121]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnanf
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadFlashParameterOne
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[118]"></a>rc_range_init</STRONG> (Thumb, 196 bytes, Stack size 32 bytes, rc.o(.text.rc_range_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = rc_range_init &rArr; __aeabi_fmul
</UL>
<BR>[Calls]<UL><LI><a href="#[109]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_memcpy4
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11a]"></a>rgb_init</STRONG> (Thumb, 8 bytes, Stack size 8 bytes, rgb.o(.text.rgb_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 80<LI>Call Chain = rgb_init &rArr; rgb_start_bling &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[122]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_start_bling
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[122]"></a>rgb_start_bling</STRONG> (Thumb, 76 bytes, Stack size 32 bytes, rgb.o(.text.rgb_start_bling))
<BR><BR>[Stack]<UL><LI>Max Depth = 72<LI>Call Chain = rgb_start_bling &rArr; bling_set
</UL>
<BR>[Calls]<UL><LI><a href="#[95]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bling_set
</UL>
<BR>[Called By]<UL><LI><a href="#[11a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rgb_init
</UL>

<P><STRONG><a name="[117]"></a>simulation_pwm_init</STRONG> (Thumb, 12 bytes, Stack size 8 bytes, sensor.o(.text.simulation_pwm_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = simulation_pwm_init &rArr; DL_GPIO_clearPins
</UL>
<BR>[Calls]<UL><LI><a href="#[123]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[11d]"></a>speed_control_100hz</STRONG> (Thumb, 1232 bytes, Stack size 264 bytes, motor_control.o(.text.speed_control_100hz))
<BR><BR>[Stack]<UL><LI>Max Depth = 312<LI>Call Chain = speed_control_100hz &rArr; constrain_float &rArr; __ARM_isnan
</UL>
<BR>[Calls]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[77]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fmul
<LI><a href="#[30]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_i2f
<LI><a href="#[120]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fcmpgt
<LI><a href="#[91]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fsub
<LI><a href="#[78]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[f7]"></a>spi_read_write_byte</STRONG> (Thumb, 44 bytes, Stack size 16 bytes, w25qxx.o(.text.spi_read_write_byte))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[126]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_receiveData8
<LI><a href="#[125]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_isBusy
<LI><a href="#[124]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_transmitData8
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_read
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_readID
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
</UL>

<P><STRONG><a name="[8b]"></a>ssd1306_begin</STRONG> (Thumb, 316 bytes, Stack size 40 bytes, ssd1306.o(.text.ssd1306_begin))
<BR><BR>[Stack]<UL><LI>Max Depth = 160<LI>Call Chain = ssd1306_begin &rArr; ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[127]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_command
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[127]"></a>ssd1306_command</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, ssd1306.o(.text.ssd1306_command))
<BR><BR>[Stack]<UL><LI>Max Depth = 120<LI>Call Chain = ssd1306_command &rArr; OLED_WrCmd &rArr; bsp_analog_i2c_send_byte &rArr; bsp_analog_i2c_wait_ack &rArr; i2c_sda_in &rArr; DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Calls]<UL><LI><a href="#[80]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_WrCmd
</UL>
<BR>[Called By]<UL><LI><a href="#[8b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ssd1306_begin
</UL>

<P><STRONG><a name="[116]"></a>trackless_params_init</STRONG> (Thumb, 1844 bytes, Stack size 264 bytes, sdk.o(.text.trackless_params_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 392<LI>Call Chain = trackless_params_init &rArr; ReadFlashParameterThree &rArr; W25QXX_Read_f &rArr; W25Q64_read &rArr; spi_read_write_byte &rArr; DL_SPI_transmitData8
</UL>
<BR>[Calls]<UL><LI><a href="#[98]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadFlashParameterThree
<LI><a href="#[96]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ReadFlashParameterOne
<LI><a href="#[128]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__ARM_isnanf
<LI><a href="#[fd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__aeabi_f2iz
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[115]"></a>w25qxx_gpio_init</STRONG> (Thumb, 48 bytes, Stack size 16 bytes, w25qxx.o(.text.w25qxx_gpio_init))
<BR><BR>[Stack]<UL><LI>Max Depth = 96<LI>Call Chain = w25qxx_gpio_init &rArr; Delay_Ms &rArr; delay_ms &rArr; delay &rArr; delayMicroseconds &rArr; micros
</UL>
<BR>[Calls]<UL><LI><a href="#[73]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Delay_Ms
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_readID
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
<LI><a href="#[f8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
</UL>

<P><STRONG><a name="[105]"></a>__aeabi_fcmpge</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgeq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpge
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[129]"></a>_fgeq</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgeq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[120]"></a>__aeabi_fcmpgt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fgr))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmpgt
</UL>
<BR>[Called By]<UL><LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
</UL>

<P><STRONG><a name="[12b]"></a>_fgr</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fgr), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmpge
</UL>

<P><STRONG><a name="[106]"></a>__aeabi_fcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fleq))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmple
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[12c]"></a>_fleq</STRONG> (Thumb, 26 bytes, Stack size 8 bytes, fcmp.o(i._fleq), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[11f]"></a>__aeabi_fcmplt</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, fcmp.o(i._fls))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = __aeabi_fcmplt
</UL>
<BR>[Called By]<UL><LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
</UL>

<P><STRONG><a name="[12e]"></a>_fls</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, fcmp.o(i._fls), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[12d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fcmple
</UL>

<P><STRONG><a name="[78]"></a>__aeabi_fadd</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fadd
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrtI
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[12f]"></a>_fadd</STRONG> (Thumb, 134 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[130]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub1
</UL>

<P><STRONG><a name="[12a]"></a>_fcmpge</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, fgef.o(x$fpl$fgeqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[12b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgr
<LI><a href="#[129]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fgeq
</UL>

<P><STRONG><a name="[16c]"></a>__aeabi_cfcmple</STRONG> (Thumb, 0 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)

<P><STRONG><a name="[12d]"></a>_fcmple</STRONG> (Thumb, 78 bytes, Stack size 8 bytes, flef.o(x$fpl$fleqf), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[33]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__fpl_fcmp_InfNaN
</UL>
<BR>[Called By]<UL><LI><a href="#[12e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fls
<LI><a href="#[12c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fleq
</UL>

<P><STRONG><a name="[77]"></a>__aeabi_fmul</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fmul
</UL>
<BR>[Called By]<UL><LI><a href="#[10e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;gray_turn_control_200hz
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[118]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rc_range_init
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
<LI><a href="#[75]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrt
<LI><a href="#[76]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;FastSqrtI
<LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[16d]"></a>_fmul</STRONG> (Thumb, 172 bytes, Stack size 16 bytes, fmul.o(x$fpl$fmul), UNUSED)

<P><STRONG><a name="[91]"></a>__aeabi_fsub</STRONG> (Thumb, 0 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __aeabi_fsub
</UL>
<BR>[Called By]<UL><LI><a href="#[ea]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;get_systime
<LI><a href="#[11d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;speed_control_100hz
<LI><a href="#[27]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;main
<LI><a href="#[10f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;pid_control_run
<LI><a href="#[8f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;Openmv_Data_Receive_Anl_1
</UL>

<P><STRONG><a name="[131]"></a>_fsub</STRONG> (Thumb, 204 bytes, Stack size 16 bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Calls]<UL><LI><a href="#[132]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd1
</UL>
<P>
<H3>
Local Symbols
</H3>
<P><STRONG><a name="[3d]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_adc12.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[3c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setClockConfig
</UL>

<P><STRONG><a name="[41]"></a>DL_DMA_configTransfer</STRONG> (Thumb, 84 bytes, Stack size 44 bytes, dl_dma.o(.text.DL_DMA_configTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 44<LI>Call Chain = DL_DMA_configTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>

<P><STRONG><a name="[42]"></a>DL_DMA_setTrigger</STRONG> (Thumb, 52 bytes, Stack size 32 bytes, dl_dma.o(.text.DL_DMA_setTrigger))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = DL_DMA_setTrigger &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[43]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[40]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_initChannel
</UL>

<P><STRONG><a name="[43]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_dma.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[42]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_DMA_setTrigger
</UL>

<P><STRONG><a name="[46]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_i2c.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[45]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setClockConfig
</UL>

<P><STRONG><a name="[4a]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_spi.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[49]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_init
</UL>

<P><STRONG><a name="[66]"></a>DL_Timer_setLoadValue</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, dl_timer.o(.text.DL_Timer_setLoadValue))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setLoadValue
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
</UL>

<P><STRONG><a name="[67]"></a>DL_Timer_setCounterValueAfterEnable</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Timer_setCounterValueAfterEnable))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_Timer_setCounterValueAfterEnable &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[68]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[69]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initTimerMode
</UL>

<P><STRONG><a name="[68]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_timer.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[6b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptCompUpdateMethod
<LI><a href="#[61]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareAction
<LI><a href="#[65]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_initPWMMode
<LI><a href="#[64]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareInput
<LI><a href="#[62]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCaptureCompareCtl
<LI><a href="#[67]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Timer_setCounterValueAfterEnable
</UL>

<P><STRONG><a name="[6d]"></a>DL_UART_disable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, dl_uart.o(.text.DL_UART_disable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_disable
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[6e]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_uart.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[6c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_init
</UL>

<P><STRONG><a name="[4e]"></a>DL_SYSCTL_disableSYSPLL</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_disableSYSPLL))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
</UL>

<P><STRONG><a name="[4f]"></a>DL_SYSCTL_getClockStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_getClockStatus))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
<LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
<LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
</UL>

<P><STRONG><a name="[50]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
<LI><a href="#[58]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFXTStartupTime
<LI><a href="#[57]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFXTFrequencyRange
</UL>

<P><STRONG><a name="[51]"></a>DL_CORE_getInstructionConfig</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_CORE_getInstructionConfig))
<BR><BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
</UL>

<P><STRONG><a name="[52]"></a>DL_CORE_configInstruction</STRONG> (Thumb, 28 bytes, Stack size 12 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_CORE_configInstruction))
<BR><BR>[Stack]<UL><LI>Max Depth = 12<LI>Call Chain = DL_CORE_configInstruction
</UL>
<BR>[Called By]<UL><LI><a href="#[4d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_configSYSPLL
</UL>

<P><STRONG><a name="[5f]"></a>DL_SYSCTL_setHSCLKSource</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHSCLKSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setHSCLKSource
</UL>
<BR>[Called By]<UL><LI><a href="#[5e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_switchMCLKfromSYSOSCtoHSCLK
</UL>

<P><STRONG><a name="[56]"></a>DL_SYSCTL_disableHFXT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_disableHFXT))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>

<P><STRONG><a name="[57]"></a>DL_SYSCTL_setHFXTFrequencyRange</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFXTFrequencyRange))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setHFXTFrequencyRange &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>

<P><STRONG><a name="[58]"></a>DL_SYSCTL_setHFXTStartupTime</STRONG> (Thumb, 20 bytes, Stack size 16 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_setHFXTStartupTime))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setHFXTStartupTime &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[50]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>

<P><STRONG><a name="[59]"></a>DL_SYSCTL_enableHFCLKStartupMonitor</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_enableHFCLKStartupMonitor))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>

<P><STRONG><a name="[5a]"></a>DL_SYSCTL_disableHFCLKStartupMonitor</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, dl_sysctl_mspm0g1x0x_g3x0x.o(.text.DL_SYSCTL_disableHFCLKStartupMonitor))
<BR><BR>[Called By]<UL><LI><a href="#[55]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKSourceHFXTParams
</UL>

<P><STRONG><a name="[dc]"></a>DL_GPIO_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[dd]"></a>DL_Timer_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[de]"></a>DL_I2C_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[df]"></a>DL_UART_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e0]"></a>DL_SPI_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e1]"></a>DL_ADC12_reset</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_reset))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_reset
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e2]"></a>DL_GPIO_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e3]"></a>DL_Timer_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e4]"></a>DL_I2C_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e5]"></a>DL_UART_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e6]"></a>DL_SPI_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[e7]"></a>DL_ADC12_enablePower</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enablePower))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_enablePower
</UL>
<BR>[Called By]<UL><LI><a href="#[db]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_initPower
</UL>

<P><STRONG><a name="[a4]"></a>DL_GPIO_initPeripheralAnalogFunction</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralAnalogFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initPeripheralAnalogFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a5]"></a>DL_GPIO_initPeripheralOutputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralOutputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralOutputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a6]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a7]"></a>DL_GPIO_initPeripheralInputFunctionFeatures</STRONG> (Thumb, 52 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunctionFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initPeripheralInputFunctionFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a8]"></a>DL_GPIO_enableHiZ</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableHiZ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_enableHiZ
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[a9]"></a>DL_GPIO_initPeripheralInputFunction</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initPeripheralInputFunction))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_initPeripheralInputFunction
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[aa]"></a>DL_GPIO_initDigitalInput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalInput
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ab]"></a>DL_GPIO_initDigitalInputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalInputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ac]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ad]"></a>DL_GPIO_initDigitalOutputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_GPIO_initDigitalOutputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalOutputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[ae]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[af]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[b0]"></a>DL_GPIO_setLowerPinsPolarity</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_setLowerPinsPolarity))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setLowerPinsPolarity
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[b1]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[b2]"></a>DL_GPIO_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_GPIO_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[a3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_GPIO_init
</UL>

<P><STRONG><a name="[c5]"></a>DL_SYSCTL_setBORThreshold</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setBORThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SYSCTL_setBORThreshold
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[53]"></a>DL_SYSCTL_setFlashWaitState</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setFlashWaitState))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setFlashWaitState &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5c]"></a>DL_SYSCTL_setSYSOSCFreq</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setSYSOSCFreq))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setSYSOSCFreq &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[c6]"></a>DL_SYSCTL_disableHFXT</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableHFXT))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[c7]"></a>DL_SYSCTL_disableSYSPLL</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_disableSYSPLL))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5d]"></a>DL_SYSCTL_setULPCLKDivider</STRONG> (Thumb, 24 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setULPCLKDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setULPCLKDivider &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[54]"></a>DL_SYSCTL_setHFCLKDividerForMFPCLK</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setHFCLKDividerForMFPCLK))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setHFCLKDividerForMFPCLK &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[c8]"></a>DL_SYSCTL_enableMFCLK</STRONG> (Thumb, 16 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFCLK))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[c9]"></a>DL_SYSCTL_enableMFPCLK</STRONG> (Thumb, 12 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_enableMFPCLK))
<BR><BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[5b]"></a>DL_SYSCTL_setMFPCLKSource</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_setMFPCLKSource))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SYSCTL_setMFPCLKSource &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
</UL>

<P><STRONG><a name="[9f]"></a>__NVIC_SetPriority</STRONG> (Thumb, 124 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.__NVIC_SetPriority))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = __NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
<LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
<LI><a href="#[c4]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_init
<LI><a href="#[cb]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SysTick_Config
</UL>

<P><STRONG><a name="[bb]"></a>DL_Timer_enableClock</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableClock))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_enableClock
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
<LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[bc]"></a>DL_Timer_setCCPDirection</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_setCCPDirection))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_setCCPDirection
</UL>
<BR>[Called By]<UL><LI><a href="#[be]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_2_init
<LI><a href="#[bd]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_1_init
<LI><a href="#[b9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_PWM_0_init
</UL>

<P><STRONG><a name="[cd]"></a>DL_Timer_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_Timer_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Timer_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[cf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G12_init
<LI><a href="#[d0]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_G8_init
<LI><a href="#[ce]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_2_init
<LI><a href="#[cc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_TIMER_1_init
</UL>

<P><STRONG><a name="[44]"></a>DL_I2C_setAnalogGlitchFilterPulseWidth</STRONG> (Thumb, 38 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setAnalogGlitchFilterPulseWidth))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setAnalogGlitchFilterPulseWidth &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[b4]"></a>DL_I2C_enableAnalogGlitchFilter</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableAnalogGlitchFilter))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableAnalogGlitchFilter
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[b5]"></a>DL_I2C_resetControllerTransfer</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_resetControllerTransfer))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_resetControllerTransfer
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[b6]"></a>DL_I2C_setTimerPeriod</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_I2C_setTimerPeriod))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_I2C_setTimerPeriod
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[48]"></a>DL_I2C_setControllerTXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setControllerTXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setControllerTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[47]"></a>DL_I2C_setControllerRXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_I2C_setControllerRXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_I2C_setControllerRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[b7]"></a>DL_I2C_enableControllerClockStretching</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableControllerClockStretching))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableControllerClockStretching
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[b8]"></a>DL_I2C_enableController</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_I2C_enableController))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_I2C_enableController
</UL>
<BR>[Called By]<UL><LI><a href="#[b3]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_I2C_0_init
</UL>

<P><STRONG><a name="[70]"></a>DL_UART_setOversampling</STRONG> (Thumb, 30 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_UART_setOversampling))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_UART_setOversampling &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[6f]"></a>DL_UART_setBaudRateDivisor</STRONG> (Thumb, 60 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setBaudRateDivisor))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setBaudRateDivisor &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[d3]"></a>DL_UART_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_UART_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[d4]"></a>DL_UART_enable</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[d9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_3_init
<LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
<LI><a href="#[d5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_1_init
<LI><a href="#[d1]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_0_init
</UL>

<P><STRONG><a name="[d7]"></a>DL_UART_enableDMATransmitEvent</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enableDMATransmitEvent))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enableDMATransmitEvent
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
</UL>

<P><STRONG><a name="[d8]"></a>DL_UART_enableFIFOs</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_UART_enableFIFOs))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_enableFIFOs
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
</UL>

<P><STRONG><a name="[71]"></a>DL_UART_setRXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setRXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setRXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
</UL>

<P><STRONG><a name="[72]"></a>DL_UART_setTXFIFOThreshold</STRONG> (Thumb, 36 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_UART_setTXFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_UART_setTXFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[d6]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_UART_2_init
</UL>

<P><STRONG><a name="[4b]"></a>DL_SPI_setBitRateSerialClockDivider</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_SPI_setBitRateSerialClockDivider))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_SPI_setBitRateSerialClockDivider &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[4c]"></a>DL_SPI_setFIFOThreshold</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.DL_SPI_setFIFOThreshold))
<BR><BR>[Stack]<UL><LI>Max Depth = 40<LI>Call Chain = DL_SPI_setFIFOThreshold &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[c1]"></a>DL_SPI_enable</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_SPI_enable))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_enable
</UL>
<BR>[Called By]<UL><LI><a href="#[bf]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SPI_0_init
</UL>

<P><STRONG><a name="[3e]"></a>DL_ADC12_setStartAddress</STRONG> (Thumb, 28 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_ADC12_setStartAddress))
<BR><BR>[Stack]<UL><LI>Max Depth = 32<LI>Call Chain = DL_ADC12_setStartAddress &rArr; DL_Common_updateReg
</UL>
<BR>[Calls]<UL><LI><a href="#[3f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[9b]"></a>DL_ADC12_configConversionMem</STRONG> (Thumb, 74 bytes, Stack size 28 bytes, ti_msp_dl_config.o(.text.DL_ADC12_configConversionMem))
<BR><BR>[Stack]<UL><LI>Max Depth = 28<LI>Call Chain = DL_ADC12_configConversionMem
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[9c]"></a>DL_ADC12_setSampleTime0</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_setSampleTime0))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_setSampleTime0
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[9d]"></a>DL_ADC12_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[9e]"></a>DL_ADC12_enableInterrupt</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enableInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_ADC12_enableInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[a0]"></a>DL_ADC12_enableConversions</STRONG> (Thumb, 22 bytes, Stack size 4 bytes, ti_msp_dl_config.o(.text.DL_ADC12_enableConversions))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_enableConversions
</UL>
<BR>[Called By]<UL><LI><a href="#[9a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_ADC12_0_init
</UL>

<P><STRONG><a name="[cb]"></a>SysTick_Config</STRONG> (Thumb, 68 bytes, Stack size 24 bytes, ti_msp_dl_config.o(.text.SysTick_Config))
<BR><BR>[Stack]<UL><LI>Max Depth = 48<LI>Call Chain = SysTick_Config &rArr; __NVIC_SetPriority
</UL>
<BR>[Calls]<UL><LI><a href="#[9f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;__NVIC_SetPriority
</UL>
<BR>[Called By]<UL><LI><a href="#[ca]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSTICK_init
</UL>

<P><STRONG><a name="[c3]"></a>DL_SYSCTL_getClockStatus</STRONG> (Thumb, 8 bytes, Stack size 0 bytes, ti_msp_dl_config.o(.text.DL_SYSCTL_getClockStatus))
<BR><BR>[Called By]<UL><LI><a href="#[c2]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;SYSCFG_DL_SYSCTL_CLK_init
</UL>

<P><STRONG><a name="[3f]"></a>DL_Common_updateReg</STRONG> (Thumb, 40 bytes, Stack size 16 bytes, ti_msp_dl_config.o(.text.DL_Common_updateReg))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = DL_Common_updateReg
</UL>
<BR>[Called By]<UL><LI><a href="#[3e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_ADC12_setStartAddress
<LI><a href="#[4c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setFIFOThreshold
<LI><a href="#[4b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SPI_setBitRateSerialClockDivider
<LI><a href="#[72]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setTXFIFOThreshold
<LI><a href="#[71]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setRXFIFOThreshold
<LI><a href="#[6f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setBaudRateDivisor
<LI><a href="#[70]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_UART_setOversampling
<LI><a href="#[47]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerRXFIFOThreshold
<LI><a href="#[48]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setControllerTXFIFOThreshold
<LI><a href="#[44]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_I2C_setAnalogGlitchFilterPulseWidth
<LI><a href="#[5b]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setMFPCLKSource
<LI><a href="#[54]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setHFCLKDividerForMFPCLK
<LI><a href="#[5d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setULPCLKDivider
<LI><a href="#[5c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setSYSOSCFreq
<LI><a href="#[53]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;DL_SYSCTL_setFlashWaitState
</UL>

<P><STRONG><a name="[ff]"></a>analog_i2c_delay</STRONG> (Thumb, 38 bytes, Stack size 4 bytes, oled.o(.text.analog_i2c_delay))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = analog_i2c_delay
</UL>
<BR>[Called By]<UL><LI><a href="#[8d]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_send_byte
<LI><a href="#[100]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_wait_ack
<LI><a href="#[8c]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_start
<LI><a href="#[8e]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;bsp_analog_i2c_stop
</UL>

<P><STRONG><a name="[111]"></a>DL_GPIO_initDigitalOutput</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, oled.o(.text.DL_GPIO_initDigitalOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_GPIO_initDigitalOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>

<P><STRONG><a name="[112]"></a>DL_GPIO_enableOutput</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.DL_GPIO_enableOutput))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_enableOutput
</UL>
<BR>[Called By]<UL><LI><a href="#[fe]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_out
</UL>

<P><STRONG><a name="[110]"></a>DL_GPIO_initDigitalInputFeatures</STRONG> (Thumb, 44 bytes, Stack size 24 bytes, oled.o(.text.DL_GPIO_initDigitalInputFeatures))
<BR><BR>[Stack]<UL><LI>Max Depth = 24<LI>Call Chain = DL_GPIO_initDigitalInputFeatures
</UL>
<BR>[Called By]<UL><LI><a href="#[101]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;i2c_sda_in
</UL>

<P><STRONG><a name="[89]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[8a]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, oled.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[88]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;OLED_Init
</UL>

<P><STRONG><a name="[e9]"></a>DL_Timer_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, ntimer.o(.text.DL_Timer_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_Timer_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[18]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG12_IRQHandler
<LI><a href="#[8]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG8_IRQHandler
<LI><a href="#[14]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG6_IRQHandler
<LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[ec]"></a>DL_GPIO_togglePins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, ntimer.o(.text.DL_GPIO_togglePins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_togglePins
</UL>
<BR>[Called By]<UL><LI><a href="#[13]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;TIMG0_IRQHandler
</UL>

<P><STRONG><a name="[93]"></a>__NVIC_ClearPendingIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, nppm.o(.text.__NVIC_ClearPendingIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_ClearPendingIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_Init
</UL>

<P><STRONG><a name="[94]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, nppm.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[92]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;PPM_Init
</UL>

<P><STRONG><a name="[79]"></a>DL_Interrupt_getStatusGroup</STRONG> (Thumb, 28 bytes, Stack size 8 bytes, nppm.o(.text.DL_Interrupt_getStatusGroup))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Interrupt_getStatusGroup
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[7a]"></a>DL_GPIO_getEnabledInterruptStatus</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nppm.o(.text.DL_GPIO_getEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_getEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[7c]"></a>DL_GPIO_clearInterruptStatus</STRONG> (Thumb, 24 bytes, Stack size 8 bytes, nppm.o(.text.DL_GPIO_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[7e]"></a>DL_Interrupt_clearGroup</STRONG> (Thumb, 32 bytes, Stack size 8 bytes, nppm.o(.text.DL_Interrupt_clearGroup))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_Interrupt_clearGroup
</UL>
<BR>[Called By]<UL><LI><a href="#[7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;GROUP1_IRQHandler
</UL>

<P><STRONG><a name="[11e]"></a>__NVIC_EnableIRQ</STRONG> (Thumb, 40 bytes, Stack size 4 bytes, nadc.o(.text.__NVIC_EnableIRQ))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __NVIC_EnableIRQ
</UL>
<BR>[Called By]<UL><LI><a href="#[113]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;nADC_Init
</UL>

<P><STRONG><a name="[3b]"></a>DL_ADC12_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, nadc.o(.text.DL_ADC12_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_ADC12_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[a]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;ADC0_IRQHandler
</UL>

<P><STRONG><a name="[f2]"></a>DL_UART_clearInterruptStatus</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nuart.o(.text.DL_UART_clearInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_clearInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[f0]"></a>DL_UART_getEnabledInterruptStatus</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, nuart.o(.text.DL_UART_getEnabledInterruptStatus))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_UART_getEnabledInterruptStatus
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[f1]"></a>DL_UART_receiveData</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, nuart.o(.text.DL_UART_receiveData))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_receiveData
</UL>
<BR>[Called By]<UL><LI><a href="#[9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART3_IRQHandler
<LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
<LI><a href="#[10]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART1_IRQHandler
<LI><a href="#[12]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART0_IRQHandler
</UL>

<P><STRONG><a name="[f4]"></a>DL_UART_getPendingInterrupt</STRONG> (Thumb, 18 bytes, Stack size 4 bytes, nuart.o(.text.DL_UART_getPendingInterrupt))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_UART_getPendingInterrupt
</UL>
<BR>[Called By]<UL><LI><a href="#[11]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;UART2_IRQHandler
</UL>

<P><STRONG><a name="[f8]"></a>DL_GPIO_setPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, w25qxx.o(.text.DL_GPIO_setPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_setPins
</UL>
<BR>[Called By]<UL><LI><a href="#[115]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;w25qxx_gpio_init
<LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_read
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_readID
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
</UL>

<P><STRONG><a name="[f6]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, w25qxx.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[f5]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_read
<LI><a href="#[f9]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25Q64_readID
<LI><a href="#[fc]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_WAKEUP
<LI><a href="#[fa]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;W25QXX_PowerDown
</UL>

<P><STRONG><a name="[124]"></a>DL_SPI_transmitData8</STRONG> (Thumb, 22 bytes, Stack size 8 bytes, w25qxx.o(.text.DL_SPI_transmitData8))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_SPI_transmitData8
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
</UL>

<P><STRONG><a name="[125]"></a>DL_SPI_isBusy</STRONG> (Thumb, 20 bytes, Stack size 4 bytes, w25qxx.o(.text.DL_SPI_isBusy))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_isBusy
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
</UL>

<P><STRONG><a name="[126]"></a>DL_SPI_receiveData8</STRONG> (Thumb, 16 bytes, Stack size 4 bytes, w25qxx.o(.text.DL_SPI_receiveData8))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = DL_SPI_receiveData8
</UL>
<BR>[Called By]<UL><LI><a href="#[f7]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;spi_read_write_byte
</UL>

<P><STRONG><a name="[121]"></a>__ARM_isnanf</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, us100.o(.text.__ARM_isnanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __ARM_isnanf
</UL>
<BR>[Called By]<UL><LI><a href="#[119]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;rangefinder_init
</UL>

<P><STRONG><a name="[102]"></a>__ARM_isnanf</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, wp_math.o(.text.__ARM_isnanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __ARM_isnanf
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[104]"></a>__ARM_isnan</STRONG> (Thumb, 36 bytes, Stack size 16 bytes, wp_math.o(.text.__ARM_isnan))
<BR><BR>[Stack]<UL><LI>Max Depth = 16<LI>Call Chain = __ARM_isnan
</UL>
<BR>[Called By]<UL><LI><a href="#[86]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;constrain_float
</UL>

<P><STRONG><a name="[123]"></a>DL_GPIO_clearPins</STRONG> (Thumb, 20 bytes, Stack size 8 bytes, sensor.o(.text.DL_GPIO_clearPins))
<BR><BR>[Stack]<UL><LI>Max Depth = 8<LI>Call Chain = DL_GPIO_clearPins
</UL>
<BR>[Called By]<UL><LI><a href="#[117]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;simulation_pwm_init
</UL>

<P><STRONG><a name="[128]"></a>__ARM_isnanf</STRONG> (Thumb, 24 bytes, Stack size 4 bytes, sdk.o(.text.__ARM_isnanf))
<BR><BR>[Stack]<UL><LI>Max Depth = 4<LI>Call Chain = __ARM_isnanf
</UL>
<BR>[Called By]<UL><LI><a href="#[116]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;trackless_params_init
</UL>

<P><STRONG><a name="[132]"></a>_fadd1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fadd), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[131]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fsub
</UL>

<P><STRONG><a name="[130]"></a>_fsub1</STRONG> (Thumb, 0 bytes, Stack size unknown bytes, faddsub.o(x$fpl$fsub), UNUSED)
<BR><BR>[Called By]<UL><LI><a href="#[12f]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;_fadd
</UL>
<P>
<H3>
Undefined Global Symbols
</H3>
<P><STRONG><a name="[38]"></a>_call_atexit_fns</STRONG> (ARM, 0 bytes, Stack size 0 bytes, UNDEFINED)
<BR><BR>[Called By]<UL><LI><a href="#[28]">&gt;&gt;</a>&nbsp;&nbsp;&nbsp;exit
</UL>
<HR></body></html>
